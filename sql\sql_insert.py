import psycopg2
from psycopg2 import sql
from datetime import datetime
from logger import logger
from const import PGSQL_CONFIG
from backend.query.query_live_info import query_average_enter_room_count_by_room_and_datetime, query_average_online_rank_count_by_room_and_datetime, query_total_enter_room_count_by_room_and_datetime
from sql.db_pool import get_connection
import asyncpg


async def add_average_online_rank_column():
    """
    检查 live_session_table 是否已有 ave_online_rank 列，如果没有则添加
    """
    try:
        async with get_connection() as conn:
            # 检查列是否存在
            check_column_query = """
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = 'live_session_table'
                AND column_name = 'ave_online_rank'
            );
            """
            column_exists = await conn.fetchval(check_column_query)

            if not column_exists:
                # 如果列不存在，添加列
                add_column_query = """
                ALTER TABLE live_session_table
                ADD COLUMN ave_online_rank float;
                """
                await conn.execute(add_column_query)
                logger.info("成功添加 ave_online_rank 列到 live_session_table")
            else:
                logger.info("ave_online_rank 列已存在于 live_session_table")

            return True
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"添加列时发生错误: {e}")
        return False

async def add_average_enter_room_column():
    """
    检查 live_session_table 是否已有 ave_enter_room 列，如果没有则添加
    """
    try:
        async with get_connection() as conn:
            # 检查列是否存在
            check_column_query = """
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = 'live_session_table'
                AND column_name = 'ave_enter_room'
            );
            """
            column_exists = await conn.fetchval(check_column_query)

            if not column_exists:
                # 如果列不存在，添加列
                add_column_query = """
                ALTER TABLE live_session_table
                ADD COLUMN ave_enter_room float;
                """
                await conn.execute(add_column_query)
                logger.info("成功添加 ave_enter_room 列到 live_session_table")
            else:
                logger.info("ave_enter_room 列已存在于 live_session_table")

            return True
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"添加列时发生错误: {e}")
        return False


async def add_enter_room_count_column():
    """
    检查 live_session_table 是否已有 enter_room_count 列，如果没有则添加
    """
    try:
        async with get_connection() as conn:
            # 检查列是否存在
            check_column_query = """
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = 'live_session_table'
                AND column_name = 'enter_room_count'
            );
            """
            column_exists = await conn.fetchval(check_column_query)

            if not column_exists:
                # 如果列不存在，添加列
                add_column_query = """
                ALTER TABLE live_session_table
                ADD COLUMN enter_room_count float;
                """
                await conn.execute(add_column_query)
                logger.info("成功添加 enter_room_count 列到 live_session_table")
            else:
                logger.info("enter_room_count 列已存在于 live_session_table")

            return True
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"添加列时发生错误: {e}")
        return False

async def update_average_online_rank():
    """
    更新 live_session_table 中的 ave_online_rank 列

    对 live_session_table 的每一行，取 room_id, start_time_str, end_time_str
    利用 query_ave_online_rank_count_by_room_and_datetime 计算 ave_online_rank
    然后插入到 live_session_table 的 ave_online_rank 列中
    """
    try:
        async with get_connection() as conn:
            # 获取所有需要更新的记录
            select_query = """
            SELECT id, room_id, start_time_str, end_time_str
            FROM live_session_table
            WHERE ave_online_rank IS NULL
              AND start_time_str IS NOT NULL
              AND end_time_str IS NOT NULL;
            """
            records = await conn.fetch(select_query)

            if not records:
                logger.info("没有需要更新的记录")
                return True

            logger.info(f"找到 {len(records)} 条需要更新的记录")

            # 更新每条记录
            for record in records:
                record_id, room_id, start_time_str, end_time_str = record["id"], record["room_id"], record["start_time_str"], record["end_time_str"]

                try:
                    # 转换时间字符串为 datetime 对象
                    start_datetime = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
                    end_datetime = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")

                    # 计算平均高能榜人数
                    ave_online_rank = await query_average_online_rank_count_by_room_and_datetime(
                        room_id, start_datetime, end_datetime
                    )

                    # 更新记录
                    update_query = """
                    UPDATE live_session_table
                    SET ave_online_rank = $1
                    WHERE id = $2;
                    """
                    await conn.execute(update_query, ave_online_rank, record_id)

                    logger.info(f"已更新记录 ID: {record_id}, room_id: {room_id}, ave_online_rank: {ave_online_rank}")

                except ValueError as e:
                    logger.error(f"处理记录 ID: {record_id} 时出错: 时间格式错误 - {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理记录 ID: {record_id} 时出错: {e}")
                    continue

            logger.info("所有记录更新完成")
            return True

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"更新 ave_online_rank 时发生错误: {e}")
        return False

async def update_average_enter_room():
    """
    更新 live_session_table 中的 ave_enter_room 列

    对 live_session_table 的每一行，取 room_id, start_time_str, end_time_str
    利用 query_ave_enter_room_count_by_room_and_datetime 计算 ave_enter_room
    然后插入到 live_session_table 的 ave_enter_room 列中
    """
    try:
        async with get_connection() as conn:
            # 获取所有需要更新的记录
            select_query = """
            SELECT id, room_id, start_time_str, end_time_str
            FROM live_session_table
            WHERE ave_enter_room IS NULL
              AND start_time_str IS NOT NULL
              AND end_time_str IS NOT NULL;
            """
            records = await conn.fetch(select_query)

            if not records:
                logger.info("没有需要更新的记录")
                return True

            logger.info(f"找到 {len(records)} 条需要更新的记录")

            # 更新每条记录
            for record in records:
                record_id, room_id, start_time_str, end_time_str = record["id"], record["room_id"], record["start_time_str"], record["end_time_str"]

                try:
                    # 转换时间字符串为 datetime 对象
                    start_datetime = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
                    end_datetime = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")

                    # 计算平均高能榜人数
                    ave_enter_room = await query_average_enter_room_count_by_room_and_datetime(
                        room_id, start_datetime, end_datetime
                    )

                    # 更新记录
                    update_query = """
                    UPDATE live_session_table
                    SET ave_enter_room = $1
                    WHERE id = $2;
                    """
                    await conn.execute(update_query, ave_enter_room, record_id)

                    logger.info(f"已更新记录 ID: {record_id}, room_id: {room_id}, ave_enter_room: {ave_enter_room}")

                except ValueError as e:
                    logger.error(f"处理记录 ID: {record_id} 时出错: 时间格式错误 - {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理记录 ID: {record_id} 时出错: {e}")
                    continue

            logger.info("所有记录更新完成")
            return True

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"更新 ave_enter_room 时发生错误: {e}")
        return False

async def update_enter_room_count():
    """
    更新 live_session_table 中的 enter_room_count 列

    对 live_session_table 的每一行，取 room_id, start_time_str, end_time_str
    利用 query_ave_enter_room_count_by_room_and_datetime 计算 enter_room_count
    然后插入到 live_session_table 的 enter_room_count 列中
    """
    try:
        async with get_connection() as conn:
            # 获取所有需要更新的记录
            select_query = """
            SELECT id, room_id, start_time_str, end_time_str
            FROM live_session_table
            WHERE enter_room_count IS NULL
              AND start_time_str IS NOT NULL
              AND end_time_str IS NOT NULL;
            """
            records = await conn.fetch(select_query)

            if not records:
                logger.info("没有需要更新的记录")
                return True

            logger.info(f"找到 {len(records)} 条需要更新的记录")

            # 更新每条记录
            for record in records:
                record_id, room_id, start_time_str, end_time_str = record["id"], record["room_id"], record["start_time_str"], record["end_time_str"]

                try:
                    # 转换时间字符串为 datetime 对象
                    start_datetime = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
                    end_datetime = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")

                    # 计算平均高能榜人数
                    enter_room_count = await query_total_enter_room_count_by_room_and_datetime(
                        room_id, start_datetime, end_datetime
                    )

                    # 更新记录
                    update_query = """
                    UPDATE live_session_table
                    SET enter_room_count = $1
                    WHERE id = $2;
                    """
                    await conn.execute(update_query, enter_room_count, record_id)

                    logger.info(f"已更新记录 ID: {record_id}, room_id: {room_id}, enter_room_count: {enter_room_count}")

                except ValueError as e:
                    logger.error(f"处理记录 ID: {record_id} 时出错: 时间格式错误 - {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理记录 ID: {record_id} 时出错: {e}")
                    continue

            logger.info("所有记录更新完成")
            return True

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"更新 enter_room_count 时发生错误: {e}")
        return False


async def main():
    """
    主函数，执行添加列和更新数据的操作
    """
    # 添加列（如果不存在）
    # if not await add_average_online_rank_column():
    #     logger.error("添加 ave_online_rank 列失败，终止操作")
    #     return

    # # 更新数据
    # if not await update_average_online_rank():
    #     logger.error("更新 ave_online_rank 数据失败")
    #     return

    # if not await add_average_enter_room_column():
    #     logger.error("添加 ave_enter_room 列失败，终止操作")
    #     return

    # # 更新数据
    # if not await update_average_enter_room():
    #     logger.error("更新 ave_enter_room 数据失败")
    #     return

    if not await add_enter_room_count_column():
        logger.error("添加 enter_room_count 列失败，终止操作")
        return

    # 更新数据
    if not await update_enter_room_count():
        logger.error("更新 enter_room_count 数据失败")
        return

    logger.info("操作完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
