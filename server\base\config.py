"""
Server Package Configuration
服务器包配置管理

Provides configuration management for the server package,
allowing it to work independently or with external dependencies.
为服务器包提供配置管理，允许其独立工作或使用外部依赖。
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, field

@dataclass
class ServerConfig:
    """
    Server package configuration
    服务器包配置
    """
    # Database configuration
    database_url: Optional[str] = None
    db_connection_factory: Optional[Callable] = None
    
    # Logging configuration
    logger: Optional[logging.Logger] = None
    log_level: str = "INFO"
    
    # Cookie configuration
    cookie_config_path: str = "server/config/cookie_config.json"
    
    # Scheduler configuration
    scheduler_timezone: str = "Asia/Shanghai"
    scheduler_max_workers: int = 4
    
    # Rate limiting
    enable_rate_limiting: bool = True
    default_rate_limit: int = 10  # requests per second
    
    # External dependencies
    external_dependencies: Dict[str, Any] = field(default_factory=dict)
    
    # Package mode
    standalone_mode: bool = False  # True when used as independent package

# Global configuration instance
_global_config: Optional[ServerConfig] = None

def get_config() -> ServerConfig:
    """
    Get global server configuration
    获取全局服务器配置
    """
    global _global_config
    if _global_config is None:
        _global_config = ServerConfig()
    return _global_config

def set_config(config: ServerConfig):
    """
    Set global server configuration
    设置全局服务器配置
    """
    global _global_config
    _global_config = config

def configure_server(
    database_url: Optional[str] = None,
    db_connection_factory: Optional[Callable] = None,
    logger: Optional[logging.Logger] = None,
    cookie_config_path: Optional[str] = None,
    standalone_mode: bool = False,
    **kwargs
):
    """
    Configure server package
    配置服务器包
    
    Args:
        database_url: Database connection URL
        db_connection_factory: Factory function for database connections
        logger: Logger instance
        cookie_config_path: Path to cookie configuration file
        standalone_mode: Whether to run in standalone mode
        **kwargs: Additional configuration options
    """
    config = get_config()
    
    if database_url is not None:
        config.database_url = database_url
    if db_connection_factory is not None:
        config.db_connection_factory = db_connection_factory
    if logger is not None:
        config.logger = logger
    if cookie_config_path is not None:
        config.cookie_config_path = cookie_config_path
    
    config.standalone_mode = standalone_mode
    config.external_dependencies.update(kwargs)

def get_logger() -> logging.Logger:
    """
    Get configured logger or create default one
    获取配置的日志器或创建默认日志器
    """
    config = get_config()
    
    if config.logger is not None:
        return config.logger
    
    # Create default logger
    logger = logging.getLogger("vupbi.server")
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, config.log_level))
    
    return logger

def get_database_connection():
    """
    Get database connection using configured factory
    使用配置的工厂函数获取数据库连接
    """
    config = get_config()
    
    if config.db_connection_factory is not None:
        return config.db_connection_factory()
    
    if config.database_url is not None:
        # Create simple connection using database URL
        # This is a placeholder - actual implementation would depend on the database type
        raise NotImplementedError("Direct database URL connection not implemented")
    
    if config.standalone_mode:
        raise RuntimeError(
            "No database connection configured. "
            "Please provide db_connection_factory or database_url."
        )
    
    # Try to import from parent project
    try:
        from sql.db_pool import get_connection
        return get_connection()
    except ImportError:
        raise RuntimeError(
            "No database connection available. "
            "Please configure database connection or run in standalone mode."
        )

def load_cookie_config() -> Dict[str, Any]:
    """
    Load cookie configuration
    加载Cookie配置
    """
    config = get_config()
    config_path = config.cookie_config_path
    
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            get_logger().warning(f"Failed to load cookie config from {config_path}: {e}")
    
    # Return default empty config
    return {
        "cookies": {},
        "refresh_tokens": {},
        "settings": {
            "auto_refresh": False,
            "refresh_interval": 3600
        }
    }

def get_external_dependency(name: str, default=None):
    """
    Get external dependency by name
    根据名称获取外部依赖
    """
    config = get_config()
    return config.external_dependencies.get(name, default)

def is_standalone_mode() -> bool:
    """
    Check if running in standalone mode
    检查是否在独立模式下运行
    """
    return get_config().standalone_mode

def get_project_root() -> str:
    """
    Get project root directory
    获取项目根目录
    """
    # Try to get from external dependency first
    project_root = get_external_dependency("project_root")
    if project_root:
        return project_root
    
    # Try to determine from current file location
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Go up two levels from server/config.py to project root
    return os.path.dirname(os.path.dirname(current_dir))

# Default configuration values that can be imported
DEFAULT_DELAYS = {
    "DYNAMICS_FETCH_DELAY": 10,
    "VIDEO_FETCH_DELAY": 3,
    "ALL_VIDEO_INFO_DELAY": 1,
    "VIDEO_AI_CONCLUSION_DELAY": 1,
    "TIEBA_WHOLE_DELAY": 1,
    "TIEBA_THREAD_DELAY": 0.5,
    "DAHANGHAI_LIST_FETCH_LIST_DELAY": 2,
    "FOLLOWER_LIST_FETCH_LIST_DELAY": 10,
    "FETCH_HISTORY_F_AND_D_DELAY": 10,
    "USER_CUR_DATE_DELAY": 8,
    "SQL_FAIL_DELAY": 3
}

DEFAULT_POOL_SIZES = {
    "TIEBA_INSERT_POOL_SIZE": 50,
    "COMMENT_INSERT_POOL_SIZE": 50,
    "FOLLOWER_REVIEW_INSERT_POOL_SIZE": 50
}

DEFAULT_LIMITS = {
    "FETCH_FOLLOWERS_MAX_PAGES": 20
}

# AI model configuration
DEFAULT_AI_CONFIG = {
    "AI_GEN_MODEL": "hunyuan-standard-256K"
}
