"""
Server Gate Module

Contains server implementations for different data collection services:
- Creator information server
- User information server  
- Live information server
"""

try:
    from .creator_info_server import CreatorInfoServer, CreatorInfoScheduler
    CREATOR_SERVER_AVAILABLE = True
except ImportError:
    CREATOR_SERVER_AVAILABLE = False
    CreatorInfoServer = None
    CreatorInfoScheduler = None

try:
    from .user_info_till_server import UserInfoServer
    USER_SERVER_AVAILABLE = True
except ImportError:
    USER_SERVER_AVAILABLE = False
    UserInfoServer = None

try:
    from .live_info_till_server import <PERSON><PERSON>and<PERSON> as LiveInfoHandler
    LIVE_SERVER_AVAILABLE = True
except ImportError:
    LIVE_SERVER_AVAILABLE = False
    LiveInfoHandler = None

__all__ = []

if CREATOR_SERVER_AVAILABLE:
    __all__.extend([
        "CreatorInfoServer",
        "CreatorInfoScheduler"
    ])

if USER_SERVER_AVAILABLE:
    __all__.extend([
        "UserInfoServer"
    ])

if LIVE_SERVER_AVAILABLE:
    __all__.extend([
        "LiveInfoHandler"
    ])
