from datetime import datetime
import re
import json
from typing import Literal

import psycopg2
from algos.actions.action import Action
import utils as U
from const import PGSQL_CONFIG
from langchain.schema import HumanMessage, SystemMessage
from logger import logger
from sql import user_sql as fatal_sql
from sql.db_pool import get_connection
import asyncpg


class CommentSummarize(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
        embedding_name="openai",
        vectorstore_name="Faiss",
    ):
        super().__init__(llm_name, embedding_name, "relation_sum")
        self.token_max = 5000
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)
        try:
            self.mid = str(U.get_mid_with_role_name(self.char_zh))
            if not self.mid:
                logger.error(
                    f"MID could not be determined for char {self.char_zh}, which is required for DB operations."
                )
                self.mid = None  # Or some other indicator of an invalid state
        except Exception as e:
            logger.error(f"Failed to get mid for char {self.char_zh}: {e}")
            self.mid = None

    def set_char(self, char):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)
        try:
            self.mid = str(U.get_mid_with_role_name(self.char_zh))
            if not self.mid:
                logger.error(
                    f"MID could not be determined for char {self.char_zh} in set_char."
                )
                self.mid = None
        except Exception as e:
            logger.error(f"Failed to get mid for char {self.char_zh} in set_char: {e}")
            self.mid = None
        # self.output_file_path = f"{PROJECT_ROOT}/output/{self.char_zh}/Tieba/Summary.csv"

    def render_rel_system_message(self):
        prompt = """<task_description>
你是虚拟主播vtuber/vup领域的专家，极其擅长从大量观众们在论坛/动态/视频下的评论中提取有效信息，总结虚拟主播领域热门讨论话题/给出对应热门指数/指出涉及到的评论观点。

<Notice>
1.你的回复格式为json,必须严格参考<OutputSchema>一节。每条热门话题包含了topic/rank/heat/keywords/comments，遵循类型和要求说明。
2.接受最近一周的多条观众评论，每条评论消息类型遵循<InputSchema>一节的格式，存在着大量类似的评论，需要过滤掉重复的评论。
3.虚拟直播的相关背景参考<Background>一节。
</Notice>

<Background>
**虚拟UP**：以原创的虚拟角色设定、形象为自身的代表，在视频网站、社交平台上进行娱乐活动的艺人。
**虚拟偶像**:其范围包含虚拟UP主,尤指偶像化运营的虚拟UP
**评论**:虚拟主播的观众评论可能存在大量重复话语，需要筛除大量无内容的评论，只提取带内容消息。
</Background>

<InputSchema>
{
    "create_time": str, # 评论创建时间
    "heat": str, # 评论热度
    "content": str, # 评论内容, 分为针对视频/动态/贴文的评论
}
</InputSchema>

<OutputSchema>
[
	{
		'topic': str, # 热门讨论话题内容（要求：中文，一句话标题吸引眼球，solid infomation,内容必须更具体）
		'rank': int, # 该话题在本周所有话题中的热门程度排名（要求：从1开始，排名越靠前，热度越高）
		'heat': int, # 该话题在本周的热门程度（要求：从5到1，热门评价标准：越多用户提到（高权重），输入的heat转赞评值越高（低权重））
		'keywords': list(str), # 话题关键词，不限于专有名词/讨论问题等
		'comments': [view1, view2... ], # 设计该话题的不同评论
	},
	...
]
</OutputSchema>
</task_description>
"""
        return SystemMessage(content=prompt)

    def render_rel_human_message(self, context):
        return HumanMessage(
            content=f"Think step by step. The output items should be at most 5, can be 0 (return []). The comments you need to analyse is \n{context}. Response in JSON format."
        )

    def parse_comment_text(
        self, context: str
    ):  # Renamed to avoid conflict if 'parse_comment' is a more generic parser elsewhere
        ignore_words = [
            "好好好",
            "美美美",
            "晚安",
            "妈妈妈妈",
            "牛的",
            "甜",
            "帅帅帅",
            "可爱",
            "生日快乐",
            "签签签",
            "签到"
        ]
        if context in ignore_words:
            return ""
        return re.sub(
            r"\[.*?\]", "", context
        )  # Removes content within square brackets, e.g., emoticons

    async def comment_sum_from_db(
        self,
        recent_days: int,
        batch_size: int = 100,
        out_put_num: int = 10,
        source: Literal["whole", "bili", "tieba"] = "tieba",
    ):
        if not self.mid:
            logger.error("MID is not set. Cannot fetch comments from database.")
            return {"error": "MID not configured, unable to fetch comments."}

        start_time_dt, end_time_dt = U.get_date_range(recent_days)
        if isinstance(start_time_dt, str):
            start_time_dt = datetime.fromisoformat(start_time_dt)
        if isinstance(end_time_dt, str):
            end_time_dt = datetime.fromisoformat(end_time_dt)
        whole_comment_context_list = []
        try:
            async with get_connection() as conn:
                queries = []
                if source == "bili" or source == "whole":
                    # Video comments
                    video_comment_table = fatal_sql.get_video_comment_table_name(self.mid)
                    queries.append(
                        {
                            "table": video_comment_table,
                            "column": "comment",
                            "heat_column": "like_num",
                            "time_column": "datetime",
                            "type": "video_comment",
                        }
                    )
                    # Dynamics comments
                    dynamics_comment_table = fatal_sql.get_dynamics_comment_table_name(
                        self.mid
                    )
                    queries.append(
                        {
                            "table": dynamics_comment_table,
                            "column": "comment",
                            "heat_column": "like_num",
                            "time_column": "datetime",
                            "type": "dynamic_comment",
                        }
                    )
                if source == "tieba" or source == "whole":
                    # Tieba content
                    tieba_table = fatal_sql.get_tieba_whole_table_name(self.mid)
                    queries.append(
                        {
                            "table": tieba_table,
                            "column": "text",
                            "heat_column": "view_num",
                            "time_column": "create_time",
                            "title_column": "title",
                            "type": "tieba_post",
                        }
                    )

                for query_info in queries:
                    # Check if table exists before querying
                    table_exists_result = await conn.fetchval(f"SELECT to_regclass('{query_info['table']}');")
                    if table_exists_result is None:
                        logger.warning(
                            f"Table {query_info['table']} does not exist. Skipping."
                        )
                        continue

                    sql_query = f"""
                    SELECT {query_info['column']}, {query_info['heat_column']}, {query_info['time_column']}
                    {f", {query_info['title_column']}" if query_info.get("title_column") else ""}
                    FROM {query_info['table']}
                    WHERE {query_info['time_column']} >= $1 AND {query_info['time_column']} <= $2 AND {query_info['column']} IS NOT NULL AND {query_info['column']} != ''
                    ORDER BY {query_info['time_column']} DESC;
                    """
                    try:
                        results = await conn.fetch(sql_query, start_time_dt, end_time_dt)
                        for row in results:
                            comment_text = self.parse_comment_text(row[0]) if row[0] else ""
                            if not comment_text:
                                continue

                            heat_value = row[1] if row[1] is not None else 0
                            create_time_value = (
                                row[2].strftime("%Y-%m-%d %H:%M:%S")
                                if row[2]
                                else "Unknown time"
                            )

                            content_prefix = ""
                            if query_info["type"] == "video_comment":
                                content_prefix = "针对视频评论："
                            elif query_info["type"] == "dynamic_comment":
                                content_prefix = "针对动态评论："
                            elif query_info["type"] == "tieba_post":
                                title = (
                                    row[3]
                                    if query_info.get("title_column") and row[3]
                                    else "无标题"
                                )
                                content_prefix = f"针对帖子⌈{title}⌋评论："

                            info = {
                                "create_time": create_time_value,
                                "heat": heat_value,
                                "content": f"{content_prefix}⌈{comment_text}⌋",
                            }
                            whole_comment_context_list.append(info)
                    except (psycopg2.Error, asyncpg.PostgresError) as db_err:
                        logger.error(
                            f"Database error while querying {query_info['table']}: {db_err}"
                        )
                        # Optionally, re-raise or handle more gracefully
                        # For now, just logs and continues, which might lead to empty results if all queries fail

        except (psycopg2.Error, asyncpg.PostgresError) as e:
            logger.error(f"Database connection error: {e}")
            return {
                "error": "Database connection failed."
            }

        if not whole_comment_context_list:
            logger.info("No comments found for the given criteria.")
            return []

        llm_responses_content = []
        for start in range(0, len(whole_comment_context_list), batch_size):
            logger.info(
                f"Processing batch: {start // batch_size + 1} / { (len(whole_comment_context_list) + batch_size -1) // batch_size }"
            )
            end = start + batch_size
            batch = whole_comment_context_list[start:end]
            context_str = "\n".join(
                [str(item) for item in batch]
            )

            messages = [
                self.render_rel_system_message(),
                self.render_rel_human_message(context_str),
            ]
            try:
                response = await self.llm.ainvoke(messages)
                parsed_res = await self.parse_replies(response.content)
                if parsed_res:  # Ensure parsed_res is not empty or None
                    llm_responses_content.extend(parsed_res)
                else:
                    logger.warning("Parsed LLM response was empty.")

            except Exception as e:
                logger.error(f"LLM invocation or parsing error: {e}")

        final_return_list = []
        item_idx = 0
        items_collected = 0

        if not isinstance(llm_responses_content, list):
            llm_responses_content = []

        while items_collected < out_put_num and item_idx < 100:
            found_in_pass = False
            for batch_response_list in llm_responses_content:
                if not batch_response_list:
                    continue
                if not isinstance(batch_response_list, list):
                    if isinstance(batch_response_list, dict) and batch_response_list.get("rank") == 1:  # TODO
                        final_return_list.append(batch_response_list)

                elif item_idx < len(batch_response_list):
                    item = batch_response_list[item_idx]
                    if isinstance(item, dict) and item.get("rank") == 1:  # TODO
                        final_return_list.append(item)
                        items_collected += 1
                        found_in_pass = True
                        if items_collected >= out_put_num:
                            break

            if not found_in_pass:
                break
            item_idx += 1
        return [final_return_list, llm_responses_content]

    async def parse_replies(self, replies_str: str):
        prompt = """<task_description>
你是矫正文本为json的专家，请将以下文本内容矫正为json格式，必须严格参考<Schema>一节格式，

<Notice>
1.禁止以Here's...开始回复，整个回复内容可以直接 被json.loads()解析。
2.双引号内的引号必须使用单引号，否则会导致json解析错误，会报Expecting ',' delimiter错误。
</Notice> 

<Schema>
[
	{
		'topic': str, # 热门讨论话题内容
		'rank': int, # 该话题在本周所有话题中的热门程度排名
		'heat': int, # 该话题在本周的热门程度
		'keywords': list(str), # 话题关键词
		'comments': [view1, view2... ], # 设计该话题的不同评论
	},
	...
]
</Schema>
</task_description>
"""
        retries = 0
        max_retries = 3
        last_error = ""

        # Attempt to directly parse the input string first, as it might already be valid JSON.
        try:
            # Normalize common LLM output variations before parsing
            # Remove markdown code block fences if present
            cleaned_replies_str = re.sub(
                r"^```json\s*|\s*```$", "", replies_str, flags=re.MULTILINE
            ).strip()
            res_json = json.loads(cleaned_replies_str)
            return res_json  # Return early if direct parsing succeeds
        except json.JSONDecodeError as e:
            logger.info(f"Direct JSON parsing failed: {e}. Attempting LLM correction.")
            # Fall through to LLM correction if direct parsing fails.
            last_error = str(e)

        # If direct parsing fails, proceed with LLM-based correction
        while retries < max_retries:
            try:
                # Construct message for LLM correction, including the last error for context
                human_message_content = f"Think step by step. The comments list need to correct is \n{replies_str}."
                if last_error:  # Append error info if a previous attempt failed
                    human_message_content = f"Think step by step. Json load error happens at: \n{last_error}. You need to avoid the error. The comments list you need to correct is \n{replies_str}."

                messages = [
                    SystemMessage(content=prompt),
                    HumanMessage(content=human_message_content),
                ]

                response = await self.llm.ainvoke(messages)
                corrected_res_str = response.content
                # Clean the corrected response string similarly before parsing
                cleaned_corrected_res_str = re.sub(
                    r"^```json\s*|\s*```$", "", corrected_res_str, flags=re.MULTILINE
                ).strip()

                res_json = json.loads(cleaned_corrected_res_str)
                return res_json  # Return successfully parsed JSON
            except json.JSONDecodeError as e:  # Catch JSON parsing errors specifically
                last_error = str(e)  # Update last_error with the new error
                logger.error(
                    f"LLM correction attempt {retries+1}/{max_retries} failed to produce valid JSON: {e}. Corrected string was: '{cleaned_corrected_res_str}'"
                )
                retries += 1
            except (
                Exception
            ) as e:  # Catch other potential errors during LLM invocation or processing
                last_error = str(e)  # Store general error
                logger.error(
                    f"An unexpected error occurred during LLM correction attempt {retries+1}/{max_retries}: {e}"
                )
                retries += 1

        logger.error(
            f"Failed to parse replies into JSON after {max_retries} retries. Last error: {last_error}"
        )
        # Return a structure indicating failure, e.g., an empty list or a specific error object
        # Depending on how callers handle errors, an empty list might be simplest.
        return [
            {
                "topic": "Error parsing LLM response",
                "rank": 0,
                "heat": 0,
                "keywords": ["error"],
                "comments": [f"Failed after retries: {last_error}"],
            }
        ]

    async def run(
        self,
        recent_days: int,
        source: Literal["whole", "bili", "tieba"] = "tieba",
        batch_size: int = 100,
        out_put_num: int = 10,
        *args,
        **kwargs,
    ):
        logger.info(
            f"Running CommentSummarize for {self.char_zh} with source: {source}, recent_days: {recent_days}"
        )

        if not self.mid:
            logger.error(
                f"MID not set for {self.char_zh}. Aborting comment summarization."
            )
            return []

        summaries_list = await self.comment_sum_from_db(
            recent_days=recent_days,
            batch_size=batch_size,
            out_put_num=out_put_num,
            source=source,
        )
        logger.info(
            f"CommentSummarize for {self.char_zh} completed with: {(summaries_list[0] if summaries_list else [])}"
        )

        return summaries_list
