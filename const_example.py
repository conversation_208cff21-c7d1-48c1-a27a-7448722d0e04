import os

PROJECT_ROOT = os.path.abspath(os.path.join(os.path.abspath(__file__), ".."))
DATA_PATH = PROJECT_ROOT + "data"

# BAIDU COOKIE
BDUSS = "xxxxxx"

# SQL
EXTERNAL_PGSQL=False ## 是否使用外部数据库
PGSQL_HOST="xxxxxx" ## 数据库地址
PGSQL_PORT=5432 ## 数据库端口
PGSQL_DB="vupbi" ## 数据库库名
PGSQL_USER="vupbi" ## 数据库用户名
PGSQL_PASSWORD="Password123@vupbi" ## 数据库密码

# Default
DEFAULT_LLM = "gpt-4o"
DEFAULT_EMBEDDING = "openai"

## HUNYUAN API
HUNYUAN_SECRET_ID = "xxxxxx"
HUNYUAN_SECRET_KEY = "xxxxxx"
HUNYUAN_OPENAPI_URL = "xxxxxx"
HUNYUAN_OPENAPI_KEY = "xxxxxx"

# HUNYUAN
HUNYUAN_WSID = "xxxxxx"
HUNYUAN_API_KEY = "xxxxxx"
HUNYUAN_USER_KEY = "xxxxxx"

## ANTHROPIC API
ANTHROPIC_API_URL = "xxxxxx"
ANTHROPIC_API_KEY = "xxxxxx"

# BAICHUAN API
BAICHUAN_API_KEY = "xxxxxx"

OPENROUTER_API_BASE = "https://openrouter.ai/api/v1"
OPENROUTER_API_KEY = "xxxxxx"

OLLAMA_API_BASE = "xxxxxx"
OLLAMA_API_KEY = "ollama"

LLAMA_API_BASE = "xxxxxx"
LLAMA_API_KEY = "ollama"

# EMBEDDING MODEL DICT
EMBEDDING_LOC_DICT ={
    "bge": f"{PROJECT_ROOT}/model/bge-large-zh-v1.5"
}
EMDEDDING_TOP_K = 3
EMDEDDING_SCORE_THRESHOLD = 1

LLM_MODEL_DICT = {
    "qwen2-72b": "xxxxxx",
    "qwen2-7b": "xxxxxx",
    "baichuan2-13b": "xxxxxx",
}