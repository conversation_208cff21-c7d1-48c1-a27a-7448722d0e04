# -*- coding: utf-8 -*-
# @Date    : 2024/07/15 15:37
# <AUTHOR> yuymf
# @Desc    :

import types
from typing import Any, Dict, Iterator, List, Optional
import uuid
import requests
from const import HUNYUAN_WSID, HUNYUAN_USER_KEY

from langchain_core.callbacks.manager import CallbackMana<PERSON><PERSON>or<PERSON><PERSON>un
from langchain_core.language_models.llms import LLM
from langchain_core.outputs import GenerationChunk


class HunyuanChatModel:

    def __init__(self) -> types.NoneType:
        self.ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:81/openapi/chat/completions"
        self.model = "gpt_176B_v1.3_m5.3"
        self.header = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {HUNYUAN_USER_KEY}",
            "Wsid": HUNYUAN_WSID,
        }
        self.enable_stream = False

    async def get_chat_completetion(self, content, streaming=True):
        try:
            # https://iwiki.woa.com/p/4010715517
            json_data = {
                "model": self.model,
                "query_id": "test_query_id_" + str(uuid.uuid4()),
                "messages": [
                    {"role": "user", "content": content},
                ],
                "temperature": 1,
                "top_p": 0.8,
                "top_k": 40,
                "repetition_penalty": 1,
                "output_seq_len": 1024,
                "max_input_seq_len": 2048,
                "stream": self.enable_stream,
            }
            resp = requests.post(
                self.ss_url, headers=self.header, json=json_data, stream=False
            )
            return resp.json()

        except Exception as err:
            return f"Error Happens: {err}"


class HunyuanChatLCModel(LLM):
    """
    Used for HunyuanChatLCModel.invoke("") ...
    """

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Run the LLM on the given input.

        Override this method to implement the LLM logic.

        Args:
            prompt: The prompt to generate from.
            stop: Stop words to use when generating. Model output is cut off at the
                first occurrence of any of the stop substrings.
                If stop tokens are not supported consider raising NotImplementedError.
            run_manager: Callback manager for the run.
            **kwargs: Arbitrary additional keyword arguments. These are usually passed
                to the model provider API call.

        Returns:
            The model output as a string. Actual completions SHOULD NOT include the prompt.
        """
        if stop is not None:
            raise ValueError("stop kwargs are not permitted.")
        try:
            json_data = {
                "model": self.model,
                "query_id": "test_query_id_" + str(uuid.uuid4()),
                "messages": [
                    {"role": "user", "content": prompt},
                ],
                "temperature": 1,
                "top_p": 0.8,
                "top_k": 40,
                "repetition_penalty": 1,
                "output_seq_len": 1024,
                "max_input_seq_len": 2048,
                "stream": self.enable_stream,
            }
            resp = requests.post(
                self.ss_url, headers=self.header, json=json_data, stream=False
            )
            return resp.json()

        except Exception as err:
            return f"Error Happens: {err}"

    def _stream(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[GenerationChunk]:
        """Stream the LLM on the given prompt.

        This method should be overridden by subclasses that support streaming.

        If not implemented, the default behavior of calls to stream will be to
        fallback to the non-streaming version of the model and return
        the output as a single chunk.

        Args:
            prompt: The prompt to generate from.
            stop: Stop words to use when generating. Model output is cut off at the
                first occurrence of any of these substrings.
            run_manager: Callback manager for the run.
            **kwargs: Arbitrary additional keyword arguments. These are usually passed
                to the model provider API call.

        Returns:
            An iterator of GenerationChunks.
        """
        resp = self._call(prompt, stop, run_manager, **kwargs)
        for char in resp:
            chunk = GenerationChunk(text=char)
            if run_manager:
                run_manager.on_llm_new_token(chunk.text, chunk=chunk)

            yield chunk

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """Return a dictionary of identifying parameters."""
        return {
            # The model name allows users to specify custom token counting
            # rules in LLM monitoring applications (e.g., in LangSmith users
            # can provide per token pricing for their model and monitor
            # costs for the given LLM.)
            "model_name": "HunyuanChatLCModel",
        }

    @property
    def _llm_type(self) -> str:
        """Get the type of language model used by this chat model. Used for logging purposes only."""
        return "custom"
