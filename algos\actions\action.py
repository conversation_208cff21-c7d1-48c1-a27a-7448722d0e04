from abc import ABC

from algos.provider.model_router import LLM_DICT, EMBEDDING_MODEL_DICT
from const import DEFAULT_LLM


class Action(ABC):
    def __init__(
        self,
        llm_name="gpt-4o",
        embedding_name="openai",
        name: str = "",
        context=None,
    ):
        self.name: str = name
        self.context = context
        self.prefix = ""
        self.profile = ""
        self.desc = ""
        self.content = ""
        self.instruct_content = None

        if llm_name:
            self.llm = LLM_DICT[llm_name]
        else:
            self.llm = LLM_DICT[DEFAULT_LLM]

        if embedding_name:
            self.embedding = EMBEDDING_MODEL_DICT[embedding_name]
        else:
            self.embedding = EMBEDDING_MODEL_DICT[DEFAULT_LLM]

    def set_prefix(self, prefix, profile):
        """Set prefix for later usage"""
        self.prefix = prefix
        self.profile = profile

    def __str__(self):
        return self.__class__.__name__

    def __repr__(self):
        return self.__str__()

    # async def _aask(self, prompt: str, system_msgs: Optional[list[str]] = None) -> str:
    #     """Append default prefix"""
    #     if not system_msgs:
    #         system_msgs = []
    #     system_msgs.append(self.prefix)
    #     return await self.llm.aask(prompt, system_msgs)

    async def run(self, *args, **kwargs):
        """Run action"""
        raise NotImplementedError("The run method should be implemented in a subclass.")
