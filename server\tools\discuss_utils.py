from snownlp import <PERSON>NL<PERSON>
from senta import <PERSON><PERSON>
from typing import List
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Tuple

# 1.
# recent liandong guanxi
# recent dynamics

# 2. warning: tieba: threads's high reply count / emotion
# SnowNLP / Senta / HarvestText

num_threads: int = 4


class SentaModelWrapper:
    def __init__(self, use_cuda=False):
        self.model = Senta()
        self.model.init_model(
            model_class="ernie_1.0_skep_large_ch",
            task="sentiment_classify",
            use_cuda=use_cuda,
        )

    def analyze_sentiment(self, text: str) -> Tuple[str, str, float]:
        result = self.model.predict([text])
        text, sentiment_label, confidence = result[0]
        return text, sentiment_label, confidence


models = [SentaModelWrapper(use_cuda=False) for _ in range(num_threads)]


def senta_sentences_sentiment_analysis(
    texts: List[str],
) -> List[Tuple[str, str, float]]:
    """
    Args:
        texts: List[str]
        num_threads: int
        use_cuda: bool
    Returns:
        List[tuple]
    """
    results = []
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        future_to_text = {
            executor.submit(models[i % num_threads].analyze_sentiment, text): text
            for i, text in enumerate(texts)
        }
        for future in as_completed(future_to_text):
            text = future_to_text[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"{text} generated an exception: {exc}")

    return results


def snownlp_sentences_sentiment_analysis(text: str):
    s = SnowNLP(text)
    return s.sentiments


# import time
# t1 = time.time()

# logger.info(senta_sentences_sentiment_analysis(["我超，大赢！", "皮衣搭配瞳瞳的幼儿教育，我感觉要萎呀 ", "先相信，再相信，有没有可能坏女人演得更好", "牛牛要炸了", "乖乖接受调教吧", "买了 还有证书。光叔：这都是骗人的 🤭", "这叫浪漫，怎么能叫傻呢。", "红玫瑰爱看😋 虚环那个真的挺难评的，有人觉得乐，有人觉得尬，我是后者", "红玫瑰太美了，歌也好听🥰🥰🥰 虚环那个歌我正在喝水差点喷出来了  ",  "李姐确实爱蹭 直播提都没有提过，自己动态抽奖明写瞳子推荐 不是 个跟你熟吗？忘了当初星瞳把晚饭怎么被ac骂的了？", "真不熟好吧，真恶心", u"李姐就是路边一条啊，你看看哪有正常粉丝喜欢她的，秽了", "我超，大赢！", "皮衣搭配瞳瞳的幼儿教育，我感觉要萎呀 ", "先相信，再相信，有没有可能坏女人演得更好", "牛牛要炸了", "乖乖接受调教吧", "买了 还有证书。光叔：这都是骗人的 🤭", "这叫浪漫，怎么能叫傻呢。", "红玫瑰爱看😋 虚环那个真的挺难评的，有人觉得乐，有人觉得尬，我是后者", "红玫瑰太美了，歌也好听🥰🥰🥰 虚环那个歌我正在喝水差点喷出来了  ",  "李姐确实爱蹭 直播提都没有提过，自己动态抽奖明写瞳子推荐 不是 个跟你熟吗？忘了当初星瞳把晚饭怎么被ac骂的了？", "真不熟好吧，真恶心", u"李姐就是路边一条啊，你看看哪有正常粉丝喜欢她的，秽了","我超，大赢！", "皮衣搭配瞳瞳的幼儿教育，我感觉要萎呀 ", "先相信，再相信，有没有可能坏女人演得更好", "牛牛要炸了", "乖乖接受调教吧", "买了 还有证书。光叔：这都是骗人的 🤭", "这叫浪漫，怎么能叫傻呢。", "红玫瑰爱看😋 虚环那个真的挺难评的，有人觉得乐，有人觉得尬，我是后者", "红玫瑰太美了，歌也好听🥰🥰🥰 虚环那个歌我正在喝水差点喷出来了  ",  "李姐确实爱蹭 直播提都没有提过，自己动态抽奖明写瞳子推荐 不是 个跟你熟吗？忘了当初星瞳把晚饭怎么被ac骂的了？", "真不熟好吧，真恶心", u"李姐就是路边一条啊，你看看哪有正常粉丝喜欢她的，秽了", "我超，大赢！", "皮衣搭配瞳瞳的幼儿教育，我感觉要萎呀 ", "先相信，再相信，有没有可能坏女人演得更好", "牛牛要炸了", "乖乖接受调教吧", "买了 还有证书。光叔：这都是骗人的 🤭", "这叫浪漫，怎么能叫傻呢。", "红玫瑰爱看😋 虚环那个真的挺难评的，有人觉得乐，有人觉得尬，我是后者"]))

# t2 = time.time()
# print(t2-t1)


# Extract relavant infos from v ba

# 3. huatijulei: key word extraction
# recent 7 days, 3days, 1 day;
