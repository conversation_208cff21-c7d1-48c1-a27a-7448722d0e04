from transformers import AutoModelForCausalLM, AutoTokenizer

from typing import Any, Dict, Iterator, List, Optional

from langchain_core.callbacks.manager import CallbackManagerForLLMRun
from langchain_core.language_models.llms import LLM
from langchain_core.outputs import GenerationChunk

from transformers.tokenization_utils import PreTrainedTokenizer

# from vllm import SamplingParams
# from vllm import LLM as vLLM


class YiChatLCModel(LLM):
    """
    Used for YiChatLCModel.invoke("") ...
    """

    tokenizer: PreTrainedTokenizer = None
    model: Any = None

    def init(self, model_path):
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto",
        ).eval()

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Run the LLM on the given input.

        Override this method to implement the LLM logic.

        Args:
            prompt: The prompt to generate from.
            stop: Stop words to use when generating. Model output is cut off at the
                first occurrence of any of the stop substrings.
                If stop tokens are not supported consider raising NotImplementedError.
            run_manager: Callback manager for the run.
            **kwargs: Arbitrary additional keyword arguments. These are usually passed
                to the model provider API call.

        Returns:
            The model output as a string. Actual completions SHOULD NOT include the prompt.
        """
        if stop is not None:
            raise ValueError("stop kwargs are not permitted.")
        messages = [{"role": "user", "content": prompt}]
        input_ids = self.tokenizer.apply_chat_template(
            conversation=messages,
            tokenize=True,
            add_generation_prompt=True,
            return_tensors="pt",
        )
        output_ids = self.model.generate(
            input_ids.to("cuda"),
            max_length=512,
            do_sample=True,
            eos_token_id=self.tokenizer.convert_tokens_to_ids("\n"),
            repetition_penalty=1.3,
            no_repeat_ngram_size=5,
            # temperature=0.7,
            temperature=0,
            # top_k=40,
            top_p=0.8,
        )
        response = self.tokenizer.decode(
            output_ids[0][input_ids.shape[1] :], skip_special_tokens=True
        )

        return response

    def _stream(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[GenerationChunk]:
        """Stream the LLM on the given prompt.

        This method should be overridden by subclasses that support streaming.

        If not implemented, the default behavior of calls to stream will be to
        fallback to the non-streaming version of the model and return
        the output as a single chunk.

        Args:
            prompt: The prompt to generate from.
            stop: Stop words to use when generating. Model output is cut off at the
                first occurrence of any of these substrings.
            run_manager: Callback manager for the run.
            **kwargs: Arbitrary additional keyword arguments. These are usually passed
                to the model provider API call.

        Returns:
            An iterator of GenerationChunks.
        """
        resp = self._call(prompt, stop, run_manager, **kwargs)
        for char in resp:
            chunk = GenerationChunk(text=char)
            if run_manager:
                run_manager.on_llm_new_token(chunk.text, chunk=chunk)

            yield chunk

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """Return a dictionary of identifying parameters."""
        return {
            # The model name allows users to specify custom token counting
            # rules in LLM monitoring applications (e.g., in LangSmith users
            # can provide per token pricing for their model and monitor
            # costs for the given LLM.)
            "model_name": "YiChatLCModel",
        }

    @property
    def _llm_type(self) -> str:
        """Get the type of language model used by this chat model. Used for logging purposes only."""
        return "custom"


### Only support vllm
# class YiChatLCVllmModel(LLM):
#     """
#     Used for YiChatLCModel.invoke("") ...
#     """

#     tokenizer: PreTrainedTokenizer = None
#     llm: Any = None
#     sampling_params: Any = None

#     def init(self, model_path):
#         self.tokenizer = AutoTokenizer.from_pretrained(model_path)
#         self.sampling_params = SamplingParams(
#             n=1,
#             #   max_tokens=1024,
#             temperature=0.01,
#             #   top_p=0.8,
#             #   repetition_penalty=1.3,
#             stop_token_ids=[2],
#         )
#         self.llm = vLLM(model=model_path)

#     def _call(
#         self,
#         prompt: str,
#         stop: Optional[List[str]] = None,
#         run_manager: Optional[CallbackManagerForLLMRun] = None,
#         **kwargs: Any,
#     ) -> str:
#         """Run the LLM on the given input.

#         Override this method to implement the LLM logic.

#         Args:
#             prompt: The prompt to generate from.
#             stop: Stop words to use when generating. Model output is cut off at the
#                 first occurrence of any of the stop substrings.
#                 If stop tokens are not supported consider raising NotImplementedError.
#             run_manager: Callback manager for the run.
#             **kwargs: Arbitrary additional keyword arguments. These are usually passed
#                 to the model provider API call.

#         Returns:
#             The model output as a string. Actual completions SHOULD NOT include the prompt.
#         """
#         if stop is not None:
#             raise ValueError("stop kwargs are not permitted.")

#         messages = [{"role": "user", "content": prompt}]

#         text = self.tokenizer.apply_chat_template(
#             messages, tokenize=False, add_generation_prompt=True
#         )
#         outputs = self.llm.generate([text], self.sampling_params)

#         for output in outputs:
#             prompt = output.prompt
#             generated_text = output.outputs[0].text

#         return generated_text

#     def _stream(
#         self,
#         prompt: str,
#         stop: Optional[List[str]] = None,
#         run_manager: Optional[CallbackManagerForLLMRun] = None,
#         **kwargs: Any,
#     ) -> Iterator[GenerationChunk]:
#         """Stream the LLM on the given prompt.

#         This method should be overridden by subclasses that support streaming.

#         If not implemented, the default behavior of calls to stream will be to
#         fallback to the non-streaming version of the model and return
#         the output as a single chunk.

#         Args:
#             prompt: The prompt to generate from.
#             stop: Stop words to use when generating. Model output is cut off at the
#                 first occurrence of any of these substrings.
#             run_manager: Callback manager for the run.
#             **kwargs: Arbitrary additional keyword arguments. These are usually passed
#                 to the model provider API call.

#         Returns:
#             An iterator of GenerationChunks.
#         """
#         resp = self._call(prompt, stop, run_manager, **kwargs)
#         for char in resp:
#             chunk = GenerationChunk(text=char)
#             if run_manager:
#                 run_manager.on_llm_new_token(chunk.text, chunk=chunk)

#             yield chunk

#     @property
#     def _identifying_params(self) -> Dict[str, Any]:
#         """Return a dictionary of identifying parameters."""
#         return {
#             # The model name allows users to specify custom token counting
#             # rules in LLM monitoring applications (e.g., in LangSmith users
#             # can provide per token pricing for their model and monitor
#             # costs for the given LLM.)
#             "model_name": "YiChatLCModel",
#         }

#     @property
#     def _llm_type(self) -> str:
#         """Get the type of language model used by this chat model. Used for logging purposes only."""
#         return "custom"


## Using eg:
# import os
# import sys
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
# from const import *
# async def main():
#     ycm = YiChatLCVllmModel()
#     ycm.init(model_path='export/train_2024-07-24-19-32-21_1000steps')

#     while 1:
#         print("请输入问题：")
#         context=input()
#         print(f"\n{await ycm.ainvoke(context)}")

# asyncio.run(main())
