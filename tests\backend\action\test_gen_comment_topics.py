

from algos.actions.comment_sum import CommentSummarize

async def test_gen_comment_topics():

    char_zh = "星瞳"
    print("测试关系总结")


    print("测试模型：qwen3-235b")
    rs = CommentSummarize(char_zh, "qwen3-235b")  # ok
    res = await rs.run(260)

    # print("测试模型：qwen3-235b-a22b:free")
    # rs = CommentSummarize(char_zh, "qwen3-235b-a22b:free")  # ok
    # res = await rs.run(260)
    # print("测试模型：hunyuan-standard-256K") # ok
    # rs = CommentSummarize(char_zh, "hunyuan-standard-256K")
    # res = await rs.run(260)
    # print("测试模型：qwen2.5-72b")
    # rs = CommentSummarize(char_zh, "qwen2.5-72b")
    # res = await rs.run(260)

    # # print("测试模型：claude")
    # # rs = CommentSummarize(char_zh)
    # # res = await rs.run(260)

    # print("测试模型：qwen3-32b:free")
    # rs = CommentSummarize(char_zh, "qwen3-32b:free")
    # res = await rs.run(260)

    # print("测试模型：deepseek-prove-v2:free") # ok
    # rs = CommentSummarize(char_zh, "deepseek-prove-v2:free")
    # res = await rs.run(260)

    # print("测试模型：deepseek-v3-0324:free")
    # rs = CommentSummarize(char_zh, "deepseek-v3-0324:free")
    # res = await rs.run(260)

    # print("测试模型：deepseek-r1:free")
    # rs = CommentSummarize(char_zh, "deepseek-r1:free") 
    # res = await rs.run(260)


if __name__ == '__main__':
    import asyncio
    asyncio.run(test_gen_comment_topics())

