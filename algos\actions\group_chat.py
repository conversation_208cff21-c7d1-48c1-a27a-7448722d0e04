from algos.actions.chat import Chat
from algos.prompts import load_prompt
from langchain.schema import SystemMessage


class GroupChat(Chat):

    def __init__(
        self,
        char="",
        history="",
        max_len_story=200,
        llm_name="claude-3-5-sonnet",
        embedding_name="bge",
        vectorstore_name="Faiss",
    ) -> None:
        super().__init__(
            char, history, max_len_story, llm_name, embedding_name, vectorstore_name
        )

    def render_chat_system_message(self, template_name, user_name):
        system_message = SystemMessage(
            content=load_prompt("groupchat_template").format(char=self.char_zh)
        )
        return system_message
