import os

from langchain_community.embeddings import (
    HuggingFaceBgeEmbeddings,
    HuggingFaceEmbeddings,
)
from langchain_community.llms.baichuan import BaichuanLL<PERSON>
from langchain_community.vectorstores import FAISS, Chroma
from langchain_openai import Chat<PERSON>penA<PERSON>, OpenAIEmbeddings

import utils as U
from algos.provider.llm.hunyuan_new import HunyuanChatLCModel
from const import (
    ANTHROPIC_API_KEY,
    ANTHROPIC_API_URL,
    BAICHUAN_API_KEY,
    EMBEDDING_LOC_DICT,
    HUNYUAN_OPENAPI_KEY,
    HUNYUAN_OPENAPI_URL,
    LLAMA_API_BASE,
    LLAMA_API_KEY,
    OLLAMA_API_BASE,
    OLLAMA_API_KEY,
    OPENROUTER_API_BASE,
    OPENROUTER_API_KEY,
)
from logger import logger


def get_llm(model="hunyuan", temperature=0):
    if model == "hunyuan":
        llm = HunyuanChatLCModel()
    elif model == "hunyuan-standard-256K":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=HUNYUAN_OPENAPI_URL,
            openai_api_key=HUNYUAN_OPENAPI_KEY,
            model_name="hunyuan-standard-256K",
        )
    elif model == "claude-3-opus":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=ANTHROPIC_API_URL,
            openai_api_key=ANTHROPIC_API_KEY,
            model_name="claude-3-opus-20240229",
        )
    elif model == "claude-3-5-sonnet":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=ANTHROPIC_API_URL,
            openai_api_key=ANTHROPIC_API_KEY,
            model_name="claude-3-5-sonnet-20241022",
        )
    elif model == "baichuan4":
        llm = BaichuanLLM(
            temperature=temperature,
            baichuan_api_host="https://api.baichuan-ai.com/v1/chat/completions",
            baichuan_api_key=BAICHUAN_API_KEY,
            model="Baichuan4",
        )  # Baichuan-NPC-Turbo Baichuan-NPC-Lite
    elif model == "gpt-3.5-turbo":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=ANTHROPIC_API_URL,
            openai_api_key=ANTHROPIC_API_KEY,
            model_name="gpt-3.5-turbo",
        )
    elif model == "gpt-4o":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=ANTHROPIC_API_URL,
            openai_api_key=ANTHROPIC_API_KEY,
            model_name="gpt-4o",
        )
    elif model == "reflection-70b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OPENROUTER_API_BASE,
            openai_api_key=OPENROUTER_API_KEY,
            model_name="mattshumer/reflection-70b:free",
        )
    elif model == "deepseek-prover-v2:free":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OPENROUTER_API_BASE,
            openai_api_key=OPENROUTER_API_KEY,
            model_name="deepseek/deepseek-prover-v2:free",
        )
    elif model == "qwen3-235b-a22b:free":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OPENROUTER_API_BASE,
            openai_api_key=OPENROUTER_API_KEY,
            model_name="qwen/qwen3-235b-a22b:free",
        )
    elif model == "qwen/qwen3-32b:free":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OPENROUTER_API_BASE,
            openai_api_key=OPENROUTER_API_KEY,
            model_name="qwen/qwen3-235b-a22b:free",
        )
    elif model == "deepseek-chat-v3-0324:free":
        llm = ChatOpenAI(
            temperature=0.6,
            openai_api_base=OPENROUTER_API_BASE,
            openai_api_key=OPENROUTER_API_KEY,
            model_name="deepseek/deepseek-chat-v3-0324:free",
        )
    elif model == "deepseek-r1:free":
        llm = ChatOpenAI(
            temperature=0.6,
            openai_api_base=OPENROUTER_API_BASE,
            openai_api_key=OPENROUTER_API_KEY,
            model_name="deepseek/deepseek-r1:free",
        )
    elif model == "qwen2.5-72b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OLLAMA_API_BASE,
            openai_api_key=OLLAMA_API_KEY,
            model_name="qwen2.5:72b",
        )
    elif model == "qwen2.5-72b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OLLAMA_API_BASE,
            openai_api_key=OLLAMA_API_KEY,
            model_name="qwen2.5:72b",
        )
    elif model == "qwen3-235b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OLLAMA_API_BASE,
            openai_api_key=OLLAMA_API_KEY,
            model_name="qwen3:235b ",
        )
    elif model == "llama3.1":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=LLAMA_API_BASE,
            openai_api_key=LLAMA_API_KEY,
            model_name="llama3.1:70b",
        )
    elif model == "yi":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=OLLAMA_API_BASE,
            openai_api_key=OLLAMA_API_BASE,
            model_name="yi1.5:34b",
        )
    elif model == "claude-chat":
        llm = ChatOpenAI(
            temperature=0.7,
            openai_api_base=ANTHROPIC_API_URL,
            openai_api_key=ANTHROPIC_API_KEY,
            model_name="claude-3-5-sonnet-20241022",
        )
    elif model == "deepseek":
        llm = ChatOpenAI(
            openai_api_base=ANTHROPIC_API_URL,
            openai_api_key=ANTHROPIC_API_KEY,
            model_name="deepseek-chat",
        )
    else:
        return NotImplementedError
    return llm


def get_vectorstore(splits, embedding, vector_name="Chroma", embed_dir="embed"):
    if not os.path.exists(embed_dir):
        os.makedirs(embed_dir, exist_ok=True)
    if vector_name == "Chroma":
        # vectorstore = Chroma.from_documents(documents=splits, embedding=embedding)
        vector_store = Chroma(
            embedding_function=embedding,
            persist_directory=embed_dir,
        )
        vector_store.add_documents(splits)

    elif vector_name == "Faiss":
        if os.path.exists(embed_dir) and not U.is_dir_empty(embed_dir, ".faiss"):
            vectorstore = FAISS.load_local(
                embed_dir, embedding, allow_dangerous_deserialization=True
            )
            logger.info(f"Load embeddings from local in {embed_dir}")
            # loaded_store.merge_from(vectorstore) # TODO merge
            # vectorstore = loaded_store
        else:
            vectorstore = FAISS.from_documents(documents=splits, embedding=embedding)
            vectorstore.save_local(folder_path=embed_dir)
    else:
        return NotImplementedError
    return vectorstore


def load_embeddings(embed_model, device="cpu"):
    logger.info(f"Start load embeddings for {embed_model}")
    if embed_model == "bge":
        embeddings = HuggingFaceBgeEmbeddings(
            model_name=EMBEDDING_LOC_DICT[embed_model],
            model_kwargs={"device": device},
        )
    elif embed_model == "openai":
        embeddings = OpenAIEmbeddings(
            base_url=ANTHROPIC_API_URL, api_key=ANTHROPIC_API_KEY
        )
    elif embed_model == "hg":
        embeddings = HuggingFaceEmbeddings(
            model_name=EMBEDDING_LOC_DICT[embed_model],
            model_kwargs={"device": device},
        )
    else:
        raise NotImplementedError
    return embeddings


BAICHUAN_LLM = get_llm(model="baichuan4")
YI_LLM = get_llm(model="yi")
HUNYUAN_LLM = get_llm(model="hunyuan")
HUNYUAN_LL_LLM = get_llm(model="hunyuan-standard-256K")
CLAUDE_LLM = get_llm(model="claude-3-5-sonnet")
GPT4_LLM = get_llm(model="gpt-4o")
REFLECITON_LLM = get_llm(model="reflection-70b")
QWEN_LLM = get_llm(model="qwen2-7b")
QWEN72_LLM = get_llm(model="qwen2.5-72b")
LLAMA_LLM = get_llm(model="llama3.1")
CLAUDE_CHAT_LLM = get_llm(model="claude-chat")
DEEPSEEK_LLM = get_llm(model="deepseek")

QWEN3_235B_LLM = get_llm(model="qwen3-235b-a22b:free")
QWEN3_235B_LOCAL_LLM = get_llm(model="qwen3-235b")
QWEN3_32B_LLM = get_llm(model="qwen3-32b:free")
DEEPSEEK_PROVE_V2_LLM = get_llm(model="deepseek-prover-v2:free")
DEEPSEEK_V3_0324_LLM = get_llm(model="deepseek-chat-v3-0324:free")
DEEPSEEK_V1_LLM = get_llm(model="deepseek-r1:free")

logger.info("Start load llms...")

LLM_DICT = {
    "yi": YI_LLM,
    "hunyuan": HUNYUAN_LLM,
    "hunyuan-standard-256K": HUNYUAN_LL_LLM,
    "claude-3-5-sonnet": CLAUDE_LLM,
    "gpt-4o": GPT4_LLM,
    "baichuan4": BAICHUAN_LLM,
    "reflection-70b": REFLECITON_LLM,
    "qwen2-7b": QWEN_LLM,
    "qwen2.5-72b": QWEN72_LLM,
    "llama3.1": LLAMA_LLM,
    "claude-chat": CLAUDE_CHAT_LLM,
    "deepseek": DEEPSEEK_LLM,
    "qwen3-235b-a22b:free": QWEN3_235B_LLM,
    "qwen3-235b": QWEN3_235B_LOCAL_LLM,
    "qwen3-32b:free": QWEN3_32B_LLM,
    "deepseek-prove-v2:free": DEEPSEEK_PROVE_V2_LLM,
    "deepseek-v3-0324:free": DEEPSEEK_V3_0324_LLM,
    "deepseek-r1:free": DEEPSEEK_V1_LLM,
}

EMBEDDING_LOC_DICT = {}  # noqa: F811

# BGE_EMBEDDING = load_embeddings(embed_model="bge")
# HG_EMBEDDING = load_embeddings(embed_model="hg")
OPENAI_EMBEDDING = load_embeddings(embed_model="openai")

EMBEDDING_MODEL_DICT = {
    # "bge": BGE_EMBEDDING,
    # "hg": HG_EMBEDDING,
    "openai": OPENAI_EMBEDDING,
}
