import asyncio
import os

import psycopg2
from algos.prompts import load_prompt
from algos.actions.action import Action
import utils as U
from const import PROJECT_ROOT, PGSQL_CONFIG
from langchain.schema import HumanMessage, SystemMessage
from logger import logger


class RelationshipSummarize(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="hunyuan-standard-256K",
        embedding_name="openai",
        vectorstore_name="Faiss",
    ):
        super().__init__(llm_name, embedding_name, "relation_sum")
        self.token_max = 5000

        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def set_char(self, char):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def render_rel_human_message(self):
        prompt = f"""你谁？
"""
        return HumanMessage(content=prompt)

    async def relation_sum(self, recent_days: int):
        """
        """
        human_msg = self.render_rel_human_message()
        try:
            response = await self.llm.ainvoke([human_msg])
            res = response.content
            logger.info(f"****Summerize Ralationship LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize Ralationship LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员"
        return res
    
    def parse_replies(self, replies:str):
        """
        Parse the LLM response and return a list of strings.
        """
        if replies == "无":
            return []
        replies = replies.replace('"', "'")
        if "\n" in replies:
            replies = replies.split("\n")
            replies = list(filter(lambda x: x != "", replies))
            return replies
        else:
            return [replies]
        
    async def run(self, recent_days, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        message = await self.relation_sum(recent_days)
        res = self.parse_replies(message)
        return res


async def test_summarize():
    action = RelationshipSummarize()
    res = await action.run(1)
    print(res)

if __name__ == "__main__":
    asyncio.run(test_summarize())