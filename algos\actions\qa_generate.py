import os
from typing import Optional, List

from langchain_core.prompts import <PERSON>t<PERSON>rompt<PERSON><PERSON>plate
from langchain_core.pydantic_v1 import BaseModel, Field
from logger import logger
from abc import ABC

from langchain_openai import ChatOpenAI
from langchain.text_splitter import TextSplitter, CharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import DirectoryLoader
from langchain_openai import OpenAIEmbeddings


class ParagraphTextSplitter(TextSplitter):
    def split_text(self, text):
        paragraphs = text.split("\n\n")
        return paragraphs


class QAPair(BaseModel):
    danmu: Optional[str] = Field(
        ...,
        description="The danmu by the vtuber live room's audience about questions or comments.",
    )
    answer: Optional[str] = Field(..., description="The response to the danmu")
    # history: Optional[str] = Field(..., description="The history of the conversation, help to supplement the key information of the Q&A")


class ExtractionData(BaseModel):
    """Extracted information about the Q&A"""

    qa_pairs: List[QAPair]


### Background
# The following is a Chinese vtuberlive streaming record. The following is a recording of the virtual streamer {character} in {time}, titled {title}. You need to extract the QA pair from it.
# 2. Except for the fact that the answer matches the question and the semantics do not make sense and need to be slightly modified, it is better to be consistent in other cases.

EXTRACT_PROMPT = """You are an expert extraction algorithm. Only extract relevant information from the text. If you do not know the value of an attribute asked to extract, return null for the attribute's value. The following is a Chinese vtuber live streaming record contains the reply of vtuber to the audience's danmu.

### Requirements
1. In order to make the danmu and answers relevant, the danmu can be generated and paraphrased on their own, but the answers should be kept in the same text as possible.
2. The danmu needs to be in line with the audience's tone, with a tendency towards humor.
3. every pair should be individual.

### Examples
Input:
小星星说："瞳瞳今天轮到我过生日了，群星女神能助我找一个合适的工作吗？"啊，祝小星星找到一个合适的工作啊。但是小星星，如果你暂时没有找到特别适合自己的工作呢，也千万千万不要担心。因为就是...嗯，我看到有很多小星星说最近的工作可能没有那么好找啊，而且临近过年了，说什么咱都不如过个好年对吧。
Output:
danmu: "瞳瞳今天轮到我过生日了，群星女神能助我找一个合适的工作吗？"
answer: "啊，祝小星星找到一个合适的工作啊。但是小星星，如果你暂时没有找到特别适合自己的工作呢，也千万千万不要担心。因为就是...嗯，我看到有很多小星星说最近的工作可能没有那么好找啊，而且临近过年了，说什么咱都不如过个好年对吧。"

Input:
哎呀，一上来大家就一直在这里说这个房间里站不下我。小星星，你们有没有听过这样的一个笑话：就是富翁有三个女儿，他说如果你们三个谁找到什么东西把这个房间填满，那我就让谁继承我高昂的财产。第一个女儿找来了一房间的棉花没有填满，第二个女儿找到了一房间的砖头没有填满，第三个女儿找来了星瞳，于是房间里站满了人。你们听过吗，小星星？为什么我今天一上来就在讲冷笑话？啊啊啊啊，为什么我今天一上来就在讲冷笑话？
Output:
danmu: "这个房间里根本站不下主播"
answer: "哎呀，一上来大家就一直在这里说这个房间里站不下我。小星星，你们有没有听过这样的一个笑话：就是富翁有三个女儿，他说如果你们三个谁找到什么东西把这个房间填满，那我就让谁继承我高昂的财产。第一个女儿找来了一房间的棉花没有填满，第二个女儿找到了一房间的砖头没有填满，第三个女儿找来了星瞳，于是房间里站满了人。你们听过吗，小星星？为什么我今天一上来就在讲冷笑话？啊啊啊啊，为什么我今天一上来就在讲冷笑话？"

Input:
之前抽星瞳卡册的时候，抽出了两超出了大隐藏...谁问你了，谁问你了。今天在这个直播间，就算不是我打你，也有人会打你的对吧。我们一会儿公屏上就有小星星要急了啊，直接就急了。主播你自己有大隐藏吗？我有...我有个陪...我有个朋。
Output:
danmu: "之前抽星瞳卡册的时候，抽出了两抽出了大隐藏"
answer: "谁问你了，谁问你了。今天在这个直播间，就算不是我打你，也有人会打你的对吧。我们一会儿公屏上就有小星星要急了啊，直接就急了。"
"""

FANS_DICT = {"星瞳": "小星星"}


class QAGenerator(ABC):
    """
    Ref https://python.langchain.com/v0.2/docs/how_to/extraction_long_text/
    """

    def __init__(self, char="星瞳"):
        self.char = char
        self.csubtitle_dir_path = f"output/{self.char}/CleanedSubtitle"
        if not os.path.exists(self.csubtitle_dir_path):
            raise Exception("Cleaned subtitle not found.")

        self.output_dir_path = f"output/{self.char}/GenQA"
        os.makedirs(self.output_dir_path, exist_ok=True)

    def set_char(self, char):
        self.char = char
        self.csubtitle_dir_path = f"output/{self.char}/CleanedSubtitle"
        if not os.path.exists(self.csubtitle_dir_path):
            raise Exception("Cleaned subtitle not found.")

        self.output_dir_path = f"output/{self.char}/GenQA"
        os.makedirs(self.output_dir_path, exist_ok=True)

    def save_txt(self, filename, content, output_path):
        output_path = os.path.join(output_path, f"{os.path.splitext(filename)[0]}.txt")
        if isinstance(content, str):
            with open(output_path, "a", encoding="utf-8") as f:
                f.write(content)
        elif isinstance(content, list):
            with open(output_path, "a", encoding="utf-8") as f:
                for c in content:
                    f.write(c)
        else:
            raise Exception("Content type not supported.")

    def dir_loader(self, dir_path):
        loader = DirectoryLoader(
            dir_path, glob="**/*.txt", show_progress=True, use_multithreading=True
        )
        docs = loader.load()
        return docs

    def extractor(self):
        llm = ChatOpenAI(temperature=0, model_name="claude-3-5-sonnet-20240620")

        # system_message = self.render_qagen_system_message()
        # human_message = self.render_qagen_human_message(context="{text}")

        # messages = [system_message, human_message]
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", EXTRACT_PROMPT),
                ("human", "{text}"),
            ]
        )

        extractor = prompt | llm.with_structured_output(
            schema=ExtractionData,
            include_raw=False,
        )
        return extractor

    async def extract_with_rag(self):
        text_splitter = ParagraphTextSplitter(chunk_size=500, chunk_overlap=20)
        document = self.dir_loader(self.csubtitle_dir_path)
        for i, doc in enumerate(document):
            texts = text_splitter.split_text(doc.page_content)
            # logger.info(f"****QA Generator LLM Response****: {str(texts)}")
            vectorstore = FAISS.from_texts(texts, embedding=OpenAIEmbeddings())

            retriever = vectorstore.as_retriever(search_kwargs={"k": 10})

            rag_extractor = {
                "text": retriever | (lambda doc: doc[0].page_content)
            } | self.extractor()

            results = await rag_extractor.ainvoke(
                f"Question and Answer between audience {FANS_DICT[self.char]} and vtuber {self.char}, question often begins with {FANS_DICT[self.char]}说"
            )

            for qa_pair in results.qa_pairs:
                logger.info(f"****QA Generator LLM Response****: {qa_pair}")

                self.save_txt(
                    os.path.basename(doc.metadata["source"]),
                    str(qa_pair) + "\n",
                    self.output_dir_path,
                )

    def extract_brute(self):
        text_splitter = CharacterTextSplitter()
        document = self.dir_loader(self.csubtitle_dir_path)
        key_developments = []
        for i, doc in enumerate(document):
            texts = text_splitter.split_text(doc.page_content)

            # first_few = texts[:3]
            extractions = self.extractor.batch(
                [{"text": text} for text in texts],
                {"max_concurrency": 10},
            )

            for extraction in extractions:
                key_developments.extend(extraction.key_developments)

        logger.info(f"****QA Generator LLM Response****: {key_developments[:10]}")

        self.save_txt(
            os.path.basename(doc.metadata["source"]),
            str(key_developments) + "\n",
            self.output_dir_path,
        )
