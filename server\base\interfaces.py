"""
Server Package Interfaces
服务器包接口定义

Defines abstract interfaces for external dependencies,
allowing the server package to work with different implementations.
定义外部依赖的抽象接口，允许服务器包与不同的实现配合工作。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncContextManager
from datetime import datetime

class DatabaseConnectionInterface(ABC):
    """
    Abstract interface for database connections
    数据库连接抽象接口
    """
    
    @abstractmethod
    async def execute(self, query: str, *args) -> None:
        """Execute a query without returning results"""
        pass
    
    @abstractmethod
    async def fetch(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute a query and return all results"""
        pass
    
    @abstractmethod
    async def fetchrow(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """Execute a query and return first result"""
        pass
    
    @abstractmethod
    async def fetchval(self, query: str, *args) -> Any:
        """Execute a query and return single value"""
        pass
    
    @abstractmethod
    async def executemany(self, query: str, args_list: List) -> None:
        """Execute a query multiple times with different parameters"""
        pass

class DatabasePoolInterface(ABC):
    """
    Abstract interface for database connection pool
    数据库连接池抽象接口
    """
    
    @abstractmethod
    def get_connection(self) -> AsyncContextManager[DatabaseConnectionInterface]:
        """Get a database connection from the pool"""
        pass

class LoggerInterface(ABC):
    """
    Abstract interface for logging
    日志记录抽象接口
    """
    
    @abstractmethod
    def debug(self, message: str, *args, **kwargs) -> None:
        """Log debug message"""
        pass
    
    @abstractmethod
    def info(self, message: str, *args, **kwargs) -> None:
        """Log info message"""
        pass
    
    @abstractmethod
    def warning(self, message: str, *args, **kwargs) -> None:
        """Log warning message"""
        pass
    
    @abstractmethod
    def error(self, message: str, *args, **kwargs) -> None:
        """Log error message"""
        pass
    
    @abstractmethod
    def exception(self, message: str, *args, **kwargs) -> None:
        """Log exception with traceback"""
        pass

class ConfigProviderInterface(ABC):
    """
    Abstract interface for configuration provider
    配置提供者抽象接口
    """
    
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        pass
    
    @abstractmethod
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        pass
    
    @abstractmethod
    def get_cookie_config(self) -> Dict[str, Any]:
        """Get cookie configuration"""
        pass

class UserDataQueryInterface(ABC):
    """
    Abstract interface for user data queries
    用户数据查询抽象接口
    """
    
    @abstractmethod
    async def query_recent_info(self, uid: str, **kwargs) -> Dict[str, Any]:
        """Query recent user information"""
        pass
    
    @abstractmethod
    async def query_user_stats(self, uid: str, **kwargs) -> Dict[str, Any]:
        """Query user statistics"""
        pass

class CreatorDataQueryInterface(ABC):
    """
    Abstract interface for creator data queries
    创作者数据查询抽象接口
    """
    
    @abstractmethod
    async def query_creator_stats(self, uid: str, **kwargs) -> Dict[str, Any]:
        """Query creator statistics"""
        pass

class LiveDataQueryInterface(ABC):
    """
    Abstract interface for live data queries
    直播数据查询抽象接口
    """
    
    @abstractmethod
    async def query_live_info(self, room_id: str, **kwargs) -> Dict[str, Any]:
        """Query live room information"""
        pass

class UtilsInterface(ABC):
    """
    Abstract interface for utility functions
    工具函数抽象接口
    """
    
    @abstractmethod
    def safe_int(self, value: Any) -> int:
        """Safely convert value to integer"""
        pass
    
    @abstractmethod
    def format_datetime(self, dt: datetime) -> str:
        """Format datetime to string"""
        pass

class ActionInterface(ABC):
    """
    Abstract interface for AI actions
    AI动作抽象接口
    """
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the action"""
        pass

# Concrete implementations for standalone mode
class SimpleLogger(LoggerInterface):
    """Simple logger implementation for standalone mode"""
    
    def __init__(self, name: str = "vupbi.server"):
        import logging
        self.logger = logging.getLogger(name)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def debug(self, message: str, *args, **kwargs) -> None:
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs) -> None:
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs) -> None:
        self.logger.error(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs) -> None:
        self.logger.exception(message, *args, **kwargs)

class SimpleConfigProvider(ConfigProviderInterface):
    """Simple configuration provider for standalone mode"""
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        self.config = config_dict or {}
    
    def get(self, key: str, default: Any = None) -> Any:
        return self.config.get(key, default)
    
    def get_database_config(self) -> Dict[str, Any]:
        return self.config.get("database", {})
    
    def get_cookie_config(self) -> Dict[str, Any]:
        return self.config.get("cookies", {})

class SimpleUtils(UtilsInterface):
    """Simple utility functions for standalone mode"""
    
    def safe_int(self, value: Any) -> int:
        if value is None:
            return 0
        try:
            return int(value)
        except (ValueError, TypeError):
            return 0
    
    def format_datetime(self, dt: datetime) -> str:
        return dt.strftime("%Y-%m-%d %H:%M:%S")

# Factory functions for creating default implementations
def create_simple_logger(name: str = "vupbi.server") -> LoggerInterface:
    """Create a simple logger implementation"""
    return SimpleLogger(name)

def create_simple_config_provider(config: Optional[Dict[str, Any]] = None) -> ConfigProviderInterface:
    """Create a simple configuration provider"""
    return SimpleConfigProvider(config)

def create_simple_utils() -> UtilsInterface:
    """Create simple utility functions"""
    return SimpleUtils()

# Interface registry for dependency injection
_interface_registry: Dict[str, Any] = {}

def register_interface(name: str, implementation: Any) -> None:
    """Register an interface implementation"""
    _interface_registry[name] = implementation

def get_interface(name: str, default_factory=None) -> Any:
    """Get registered interface implementation"""
    if name in _interface_registry:
        return _interface_registry[name]
    
    if default_factory:
        implementation = default_factory()
        register_interface(name, implementation)
        return implementation
    
    raise ValueError(f"No implementation registered for interface: {name}")

def clear_interfaces() -> None:
    """Clear all registered interfaces"""
    _interface_registry.clear()

# Common interface names
LOGGER_INTERFACE = "logger"
CONFIG_PROVIDER_INTERFACE = "config_provider"
DATABASE_POOL_INTERFACE = "database_pool"
USER_DATA_QUERY_INTERFACE = "user_data_query"
CREATOR_DATA_QUERY_INTERFACE = "creator_data_query"
LIVE_DATA_QUERY_INTERFACE = "live_data_query"
UTILS_INTERFACE = "utils"
