"""
Creator Info Scheduler
Control script for Creator Info Scheduler

Provides control functions like start, stop, status query
"""

import os
import sys
import signal
import psutil
import json
import time
from pathlib import Path


class CreatorSchedulerController:
    """Creator Info Scheduler"""
    
    def __init__(self):
        self.pid_file = "creator_scheduler.pid"
        self.status_file = "creator_scheduler_status.json"
    
    def start(self, uid="401315430", hour=2, minute=0):
        if self.is_running():
            print("Scheduler is already running")
            return False
        
        print(f"Starting Creator Info Scheduler for UID: {uid}")
        print(f"Daily execution time: {hour:02d}:{minute:02d}")
        
        import subprocess
        cmd = [
            sys.executable, 
            "creator_info_scheduler.py",
            "--mode", "monitor",
            "--uid", uid,
            "--hour", str(hour),
            "--minute", str(minute)
        ]
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            status = {
                "pid": process.pid,
                "uid": uid,
                "daily_hour": hour,
                "daily_minute": minute,
                "start_time": time.time(),
                "status": "running"
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
            
            print(f"Scheduler started with PID: {process.pid}")
            return True
            
        except Exception as e:
            print(f"Failed to start scheduler: {e}")
            return False
    
    def stop(self):
        """停止调度器"""
        if not self.is_running():
            print("Scheduler is not running")
            return False
        
        try:
            pid = self.get_pid()
            if pid:
                os.kill(pid, signal.SIGTERM)
                
                for _ in range(10):
                    if not psutil.pid_exists(pid):
                        break
                    time.sleep(1)
                
                if psutil.pid_exists(pid):
                    os.kill(pid, signal.SIGKILL)
                    print("Force killed scheduler process")
                else:
                    print("Scheduler stopped gracefully")
                
                self.cleanup_files()
                return True
                
        except Exception as e:
            print(f"Error stopping scheduler: {e}")
            return False
    
    def restart(self, uid="401315430", hour=2, minute=0):
        print("Restarting scheduler...")
        self.stop()
        time.sleep(2)
        return self.start(uid, hour, minute)
    
    def status(self):
        if not self.is_running():
            print("Scheduler Status: STOPPED")
            return
        
        try:
            with open(self.status_file, 'r') as f:
                status = json.load(f)
            
            pid = status.get('pid')
            start_time = status.get('start_time', 0)
            uptime = time.time() - start_time
            
            print("Scheduler Status: RUNNING")
            print(f"PID: {pid}")
            print(f"UID: {status.get('uid', 'Unknown')}")
            print(f"Daily execution: {status.get('daily_hour', 0):02d}:{status.get('daily_minute', 0):02d}")
            print(f"Uptime: {uptime/3600:.1f} hours")
            
            if psutil.pid_exists(pid):
                process = psutil.Process(pid)
                print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.1f} MB")
                print(f"CPU percent: {process.cpu_percent():.1f}%")
            
        except Exception as e:
            print(f"Error getting status: {e}")
    
    def is_running(self):
        """检查调度器是否在运行"""
        pid = self.get_pid()
        return pid and psutil.pid_exists(pid)
    
    def get_pid(self):
        """获取调度器进程PID"""
        try:
            if os.path.exists(self.pid_file):
                with open(self.pid_file, 'r') as f:
                    return int(f.read().strip())
        except:
            pass
        return None
    
    def cleanup_files(self):
        """清理状态文件"""
        for file in [self.pid_file, self.status_file]:
            if os.path.exists(file):
                os.remove(file)
    
    def logs(self, lines=50):
        """显示日志"""
        log_file = "logs/creator_scheduler.log"
        if not os.path.exists(log_file):
            print("Log file not found")
            return
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
            print(f"Last {len(recent_lines)} lines from {log_file}:")
            print("-" * 50)
            for line in recent_lines:
                print(line.rstrip())
                
        except Exception as e:
            print(f"Error reading log file: {e}")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Creator Info Scheduler Controller')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status', 'logs'],
                       help='控制操作')
    parser.add_argument('--uid', default='401315430', help='用户UID')
    parser.add_argument('--hour', type=int, default=2, help='每日执行小时')
    parser.add_argument('--minute', type=int, default=0, help='每日执行分钟')
    parser.add_argument('--lines', type=int, default=50, help='显示日志行数')
    
    args = parser.parse_args()
    
    controller = CreatorSchedulerController()
    
    if args.action == 'start':
        controller.start(args.uid, args.hour, args.minute)
    elif args.action == 'stop':
        controller.stop()
    elif args.action == 'restart':
        controller.restart(args.uid, args.hour, args.minute)
    elif args.action == 'status':
        controller.status()
    elif args.action == 'logs':
        controller.logs(args.lines)


if __name__ == "__main__":
    main()
