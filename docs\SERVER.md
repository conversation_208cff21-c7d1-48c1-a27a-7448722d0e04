# 统一数据调度器使用指南
# Unified Data Scheduler User Guide

## 概述 Overview

本文档描述统一数据调度器的完整功能和使用方法，包括进程管理、监控、状态查询和优雅停止等高级功能。
This document describes the complete functionality and usage of the unified data scheduler, including process management, monitoring, status queries, and graceful shutdown features.

## 架构对比 Architecture Comparison

### 原有架构 Original Architecture
```
server/
├── till_main.py                    # 主轮询脚本
├── user_info_till_server.py        # 用户数据轮询
├── live_info_till_server.py        # 直播数据轮询
├── creator_info_scheduler_main.py  # 创作者调度器
└── backend/till_server.py          # 后端轮询服务
```

### 新架构 New Architecture
```
server/
├── unified_scheduler_main.py       # 统一调度器主入口
├── unified_data_scheduler.py       # 统一调度器核心
├── unified_scheduler_config.py     # 统一配置管理
└── scripts/
    ├── start_unified_scheduler.bat  # Windows启动脚本
    └── start_unified_scheduler.sh   # Linux/macOS启动脚本
```

## 功能映射 Function Mapping

### 用户数据 User Data
| 原有函数 Original Function | 新架构位置 New Location | 执行频率 Frequency |
|---------------------------|------------------------|-------------------|
| `user_info_1_minute_worker()` | `execute_user_functions_safely('minute')` | 每分钟 Every minute |
| `user_info_hourly_worker()` | `execute_user_functions_safely('hourly')` | 每小时 Every hour |
| `user_info_1_day_worker()` | `execute_user_functions_safely('daily')` | 每日 Daily |
| `user_info_week_worker()` | `execute_user_functions_safely('weekly')` | 每周 Weekly |
| `user_info_till_worker()` | `execute_user_functions_safely('monthly')` | 每月 Monthly |

### 创作者数据 Creator Data
| 原有函数 Original Function | 新架构位置 New Location |
|---------------------------|------------------------|
| `CreatorInfoScheduler` | `execute_creator_functions_safely()` |

### 直播数据 Live Data
| 原有函数 Original Function | 新架构位置 New Location |
|---------------------------|------------------------|
| `run_multi_clients()` | `execute_live_monitoring()` |

## 迁移步骤 Migration Steps

### 1. 停止现有服务 Stop Existing Services
```bash
# 停止现有的轮询服务
# Stop existing polling services
pkill -f till_main.py
pkill -f creator_info_scheduler.py
pkill -f live_info_till_server.py
```

### 2. 配置新系统 Configure New System
```bash
# 编辑配置文件
# Edit configuration file
python -c "from server.unified_scheduler_config import load_unified_config; print('Config loaded')"
```

### 3. 测试新系统 Test New System
```bash
# 测试运行一次
# Test run once
cd server
python unified_scheduler_main.py --mode once --data-types user

# 查看状态
# Check status
python unified_scheduler_main.py --mode status
```

### 4. 启动新系统 Start New System
```bash
# Windows
scripts\start_unified_scheduler.bat --mode monitor --data-types all

# Linux/macOS
chmod +x scripts/start_unified_scheduler.sh
scripts/start_unified_scheduler.sh --mode monitor --data-types all
```

## 使用方法 Usage

### 环境准备 Environment Setup
```bash
# 激活conda环境
conda activate clct

# 确保在项目根目录
cd /path/to/vupbi
```

### 命令行参数 Command Line Arguments
```bash
python server/unified_scheduler_main.py [options]

Options:
  --mode MODE         运行模式 (monitor|once|status|stop|help) [default: monitor]
  --data-types TYPES  数据类型，逗号分隔 (user,creator,live,all) [default: all enabled types]
  --uid UID          用户UID [default: 401315430]
  --help             显示帮助信息
```

### 数据类型选择 Data Type Selection
- `user`: 用户数据（粉丝数、动态、视频等）
- `creator`: 创作者数据（现有creator_info_server功能）
- `live`: 直播数据（直播状态、弹幕等）
- `all`: 所有数据类型

### 运行模式详解 Run Modes Detailed

#### 1. monitor 模式 - 监控模式
持续运行并自动重启，具有完整的进程管理功能：
- **自动重启**: 进程崩溃时自动重启
- **健康检查**: 每分钟进行健康检查
- **错误限制**: 超过最大错误次数时停止重启
- **状态记录**: 实时记录运行状态到文件
- **优雅停止**: 支持信号处理和优雅关闭

```bash
# 启动监控模式（所有数据类型）
python server/unified_scheduler_main.py --mode monitor

# 启动监控模式（仅用户数据）
python server/unified_scheduler_main.py --mode monitor --data-types user

# 后台运行
nohup python server/unified_scheduler_main.py --mode monitor > scheduler.log 2>&1 &
```

#### 2. once 模式 - 单次执行
执行一次后退出，用于测试和调试：
- **快速测试**: 验证配置和功能是否正常
- **调试模式**: 便于查看详细日志
- **数据验证**: 检查数据拉取是否正常

```bash
# 执行一次所有任务
python server/unified_scheduler_main.py --mode once

# 仅执行用户数据任务
python server/unified_scheduler_main.py --mode once --data-types user

# 仅执行创作者数据任务
python server/unified_scheduler_main.py --mode once --data-types creator
```

#### 3. status 模式 - 状态查询
显示详细的系统状态信息：
- **配置信息**: 显示当前启用的数据类型
- **进程状态**: 显示所有运行中的调度器进程
- **资源使用**: 显示CPU和内存使用情况
- **文件信息**: 显示PID文件、状态文件和日志文件
- **健康状态**: 显示最后健康检查时间和错误计数

```bash
# 查看当前状态
python server/unified_scheduler_main.py --mode status
```

#### 4. stop 模式 - 优雅停止
优雅停止所有正在运行的调度器进程：
- **进程发现**: 自动发现所有运行中的调度器进程
- **优雅停止**: 先发送SIGTERM信号，等待进程自行退出
- **强制停止**: 超时后发送SIGKILL信号强制终止
- **清理工作**: 清理PID文件和临时文件

```bash
# 停止所有调度器进程
python server/unified_scheduler_main.py --mode stop
```

#### 5. help 模式 - 帮助信息
显示完整的使用帮助和示例：

```bash
# 显示帮助信息
python server/unified_scheduler_main.py --mode help
```

## 配置说明 Configuration

### 主配置文件 Main Configuration
配置文件位置：`server/unified_scheduler_config.json`
Configuration file location: `server/unified_scheduler_config.json`

### 配置结构 Configuration Structure
```json
{
  "scheduler": {
    "timezone": "Asia/Shanghai",
    "max_workers": 4,
    "function_delay": 5,
    "category_delay": 10
  },
  "data_types": {
    "user": {
      "enabled": true,
      "functions": {
        "hourly": ["fetch_user_follower_num", "fetch_dahanghai_num"],
        "daily": ["fetch_user_dynamics", "fetch_all_video"]
      },
      "schedule": {
        "hourly": {"interval": 1},
        "daily": {"hour": 2, "minute": 0}
      }
    }
  }
}
```

## 多线程优化 Multithreading Optimization

新架构使用asyncio.gather实现并发执行：
The new architecture uses asyncio.gather for concurrent execution:

```python
# 并发执行所有vtuber的任务
# Execute tasks for all vtubers concurrently
tasks = []
for vtuber, server in self.user_servers.items():
    tasks.append(execute_for_vtuber(vtuber, server))

results = await asyncio.gather(*tasks, return_exceptions=True)
```

## 进程管理 Process Management

### 文件结构 File Structure
统一调度器使用以下文件结构进行进程管理：

```
server/runtime/
├── unified_scheduler.pid          # 进程PID文件
├── unified_scheduler.status       # 状态信息文件
└── logs/                         # 日志目录
    ├── scheduler_YYYYMMDD.log    # 日常日志
    └── error_YYYYMMDD.log        # 错误日志
```

### PID文件管理 PID File Management
- **自动创建**: 启动时自动创建PID文件
- **进程跟踪**: 记录主进程和工作进程的PID
- **自动清理**: 正常退出时自动删除PID文件
- **冲突检测**: 启动时检查是否有其他实例运行

### 状态文件 Status File
状态文件 `server/runtime/unified_scheduler.status` 包含：
```json
{
  "status": "running",
  "message": "Scheduler started successfully",
  "pid": 12345,
  "data_types": ["user", "creator", "live"],
  "uid": "401315430",
  "start_time": "2025-08-15T14:16:07.380000",
  "last_health_check": "2025-08-15T14:17:07.380000",
  "error_count": 0,
  "is_running": true,
  "timestamp": "2025-08-15T14:17:07.380000"
}
```

## 监控和日志 Monitoring and Logging

### 健康检查 Health Check
监控模式下每分钟执行健康检查：
- **调度器状态**: 检查调度器是否正常运行
- **任务状态**: 检查各个数据类型的任务状态
- **错误计数**: 统计和监控错误次数
- **资源使用**: 监控CPU和内存使用情况

### 日志系统 Logging System
- **主日志**: `server/runtime/logs/` 目录
- **日志轮转**: 按日期自动轮转日志文件
- **错误日志**: 单独记录错误和异常信息
- **结构化日志**: 包含时间戳、级别、模块和详细信息

### 状态监控 Status Monitoring
```bash
# 查看详细运行状态
python server/unified_scheduler_main.py --mode status

# 实时监控日志
tail -f server/runtime/logs/scheduler_$(date +%Y%m%d).log

# 查看错误日志
tail -f server/runtime/logs/error_$(date +%Y%m%d).log

# 监控状态文件变化
watch -n 5 cat server/runtime/unified_scheduler.status
```

## 使用示例 Usage Examples

### 完整工作流程 Complete Workflow

#### 1. 首次启动 First Time Setup
```bash
# 激活环境
conda activate clct

# 检查配置
python server/unified_scheduler_main.py --mode help

# 测试运行
python server/unified_scheduler_main.py --mode once --data-types user

# 查看状态
python server/unified_scheduler_main.py --mode status
```

#### 2. 生产环境部署 Production Deployment
```bash
# 启动监控模式
python server/unified_scheduler_main.py --mode monitor --data-types all

# 或者后台运行
nohup python server/unified_scheduler_main.py --mode monitor > scheduler.log 2>&1 &

# 检查运行状态
python server/unified_scheduler_main.py --mode status

# 查看日志
tail -f server/runtime/logs/scheduler_$(date +%Y%m%d).log
```

#### 3. 维护操作 Maintenance Operations
```bash
# 优雅停止所有进程
python server/unified_scheduler_main.py --mode stop

# 检查是否完全停止
python server/unified_scheduler_main.py --mode status

# 重新启动
python server/unified_scheduler_main.py --mode monitor
```

### 测试验证 Testing and Validation

#### 功能测试 Function Testing
```bash
# 测试所有模式
python server/unified_scheduler_main.py --mode help
python server/unified_scheduler_main.py --mode status
python server/unified_scheduler_main.py --mode once --data-types user
python server/unified_scheduler_main.py --mode stop
```

#### 进程管理测试 Process Management Testing
```bash
# 启动监控模式
python server/unified_scheduler_main.py --mode monitor &

# 检查PID文件
cat server/runtime/unified_scheduler.pid

# 检查状态文件
cat server/runtime/unified_scheduler.status

# 测试优雅停止
python server/unified_scheduler_main.py --mode stop
```

## 故障排除 Troubleshooting

### 常见问题 Common Issues

#### 1. 配置相关问题 Configuration Issues

**问题**: 配置文件错误或缺失
```bash
# 检查配置文件
ls -la server/config/unified_scheduler_config.json

# 重新生成默认配置
python -c "
from server.base.unified_scheduler_config import save_unified_config, DEFAULT_CONFIG
save_unified_config(DEFAULT_CONFIG)
print('Default configuration saved')
"
```

**问题**: 数据类型配置错误
```bash
# 检查启用的数据类型
python -c "
from server.base.unified_scheduler_config import get_enabled_data_types
print('Enabled data types:', get_enabled_data_types())
"
```

#### 2. 进程管理问题 Process Management Issues

**问题**: PID文件冲突
```bash
# 检查PID文件
cat server/runtime/unified_scheduler.pid

# 检查进程是否真的在运行
ps aux | grep unified_scheduler_main.py

# 手动清理PID文件（确认进程不存在后）
rm -f server/runtime/unified_scheduler.pid
```

**问题**: 进程无法停止
```bash
# 查看所有相关进程
python server/unified_scheduler_main.py --mode status

# 强制停止（如果优雅停止失败）
pkill -f unified_scheduler_main.py

# 清理残留文件
rm -f server/runtime/unified_scheduler.pid
rm -f server/runtime/unified_scheduler.status
```

#### 3. 依赖和环境问题 Dependencies and Environment Issues

**问题**: 依赖缺失
```bash
# 检查关键依赖
pip list | grep -E "(apscheduler|aiohttp|psutil|asyncpg)"

# 安装缺失依赖
pip install apscheduler aiohttp psutil asyncpg
```

**问题**: 数据库连接问题
```bash
# 测试数据库连接
python -c "
import asyncio
from sql.db_pool import get_connection
async def test():
    async with get_connection() as conn:
        result = await conn.fetchval('SELECT 1')
        print('Database connection OK:', result)
asyncio.run(test())
"
```

#### 4. 权限问题 Permission Issues

**问题**: 文件权限不足
```bash
# 检查运行时目录权限
ls -la server/runtime/

# 创建必要目录
mkdir -p server/runtime/logs

# 设置权限（Linux/macOS）
chmod 755 server/runtime
chmod 644 server/runtime/*
```

### 日志分析 Log Analysis

#### 查看运行日志 View Runtime Logs
```bash
# 查看最新日志
tail -n 100 server/runtime/logs/scheduler_$(date +%Y%m%d).log

# 搜索错误信息
grep -i error server/runtime/logs/scheduler_$(date +%Y%m%d).log

# 搜索特定模块日志
grep "user_info" server/runtime/logs/scheduler_$(date +%Y%m%d).log
```

#### 状态文件分析 Status File Analysis
```bash
# 查看当前状态
cat server/runtime/unified_scheduler.status | python -m json.tool

# 监控状态变化
watch -n 5 'cat server/runtime/unified_scheduler.status | python -m json.tool'
```

## 性能优化 Performance Optimization

### 资源监控 Resource Monitoring
```bash
# 监控进程资源使用
python server/unified_scheduler_main.py --mode status

# 使用系统工具监控
top -p $(cat server/runtime/unified_scheduler.pid)
htop -p $(cat server/runtime/unified_scheduler.pid)
```

### 配置优化 Configuration Optimization
```json
{
  "scheduler": {
    "timezone": "Asia/Shanghai",
    "max_workers": 4,              // 根据CPU核心数调整
    "function_delay": 5,           // 函数间延迟（秒）
    "category_delay": 10,          // 分类间延迟（秒）
    "error_retry_count": 3,        // 错误重试次数
    "error_retry_delay": 30        // 重试延迟（秒）
  },
  "data_types": {
    "user": {
      "limiters": {
        "hourly_task_limit": 1,    // 每小时任务限制
        "daily_task_limit": 1,     // 每日任务限制
        "long_running_task_limit": 1,  // 长时间任务限制
        "till_worker_limit": 1     // Till worker限制
      }
    }
  }
}
```

## 高级功能 Advanced Features

### 自定义数据类型 Custom Data Types
```bash
# 仅运行特定数据类型
python server/unified_scheduler_main.py --mode monitor --data-types user
python server/unified_scheduler_main.py --mode monitor --data-types creator,live
```

### 多实例管理 Multi-Instance Management
```bash
# 为不同用户运行不同实例
python server/unified_scheduler_main.py --mode monitor --uid 401315430 --data-types user &
python server/unified_scheduler_main.py --mode monitor --uid 123456789 --data-types creator &

# 查看所有实例状态
python server/unified_scheduler_main.py --mode status
```

### 定时任务配置 Scheduled Task Configuration
通过修改 `server/config/unified_scheduler_config.json` 自定义执行时间：
```json
{
  "data_types": {
    "user": {
      "schedule": {
        "hourly": {
          "interval": 1,
          "jitter": 60
        },
        "daily": {
          "hour": 2,
          "minute": 0,
          "jitter": 120
        }
      }
    }
  }
}
```

## 安全和维护 Security and Maintenance

### 安全建议 Security Recommendations
1. **文件权限**: 确保运行时文件只有必要的权限
2. **进程隔离**: 使用专用用户运行调度器
3. **日志管理**: 定期清理和轮转日志文件
4. **监控告警**: 设置监控告警机制

### 维护计划 Maintenance Schedule
1. **日常检查**: 每日检查运行状态和日志
2. **周期重启**: 每周重启一次以清理内存
3. **日志清理**: 每月清理旧日志文件
4. **配置备份**: 定期备份配置文件

```bash
# 日常维护脚本示例
#!/bin/bash
# daily_maintenance.sh

# 检查状态
python server/unified_scheduler_main.py --mode status

# 清理7天前的日志
find server/runtime/logs -name "*.log" -mtime +7 -delete

# 检查磁盘空间
df -h server/runtime/

# 发送状态报告
echo "Scheduler status: $(date)" >> maintenance.log
```

## 迁移指南 Migration Guide

### 从旧系统迁移 Migration from Legacy System

#### 停止旧服务 Stop Legacy Services
```bash
# 停止现有的轮询服务
pkill -f till_main.py
pkill -f creator_info_scheduler.py
pkill -f live_info_till_server.py
pkill -f user_info_till_server.py
```

#### 数据备份 Data Backup
```bash
# 备份配置文件
cp server/config/*.json server/config/backup/

# 备份数据库（如果需要）
pg_dump -h localhost -U postgres -d postgres > backup_$(date +%Y%m%d).sql
```

#### 配置迁移 Configuration Migration
```bash
# 检查新配置
python -c "
from server.base.unified_scheduler_config import load_unified_config
config = load_unified_config()
print('Configuration loaded successfully')
print('Enabled data types:', [k for k, v in config['data_types'].items() if v.get('enabled', False)])
"
```

#### 测试新系统 Test New System
```bash
# 测试各个模式
python server/unified_scheduler_main.py --mode help
python server/unified_scheduler_main.py --mode status
python server/unified_scheduler_main.py --mode once --data-types user

# 启动新系统
python server/unified_scheduler_main.py --mode monitor
```

### 回滚方案 Rollback Plan

如果需要回滚到原有架构：
```bash
# 1. 停止新系统
python server/unified_scheduler_main.py --mode stop

# 2. 恢复配置文件
cp server/config/backup/*.json server/config/

# 3. 启动原有系统
python till_main.py &
python creator_info_scheduler.py &
python live_info_till_server.py &
```

## 常见问题解答 FAQ

### Q: 如何确认调度器正在运行？
A: 使用 status 模式查看详细状态：
```bash
python server/unified_scheduler_main.py --mode status
```

### Q: 如何查看实时日志？
A: 使用 tail 命令监控日志文件：
```bash
tail -f server/runtime/logs/scheduler_$(date +%Y%m%d).log
```

### Q: 如何修改执行时间？
A: 编辑配置文件 `server/config/unified_scheduler_config.json` 中的 schedule 部分。

### Q: 如何添加新的数据类型？
A: 在配置文件中添加新的数据类型配置，并在代码中实现相应的处理逻辑。

### Q: 进程意外停止怎么办？
A: 检查日志文件查找错误原因，然后重新启动：
```bash
# 查看错误日志
grep -i error server/runtime/logs/scheduler_$(date +%Y%m%d).log

# 重新启动
python server/unified_scheduler_main.py --mode monitor
```

### Q: 如何监控系统资源使用？
A: 使用 status 模式查看资源使用情况，或使用系统工具：
```bash
# 查看调度器状态
python server/unified_scheduler_main.py --mode status

# 使用系统工具
top -p $(cat server/runtime/unified_scheduler.pid)
```

## 版本更新 Version Updates

### 更新流程 Update Process
1. **备份当前版本**: 备份代码和配置
2. **停止服务**: 使用 stop 模式优雅停止
3. **更新代码**: 拉取最新代码
4. **测试功能**: 使用 once 模式测试
5. **重启服务**: 使用 monitor 模式启动

```bash
# 更新示例
python server/unified_scheduler_main.py --mode stop
git pull origin main
python server/unified_scheduler_main.py --mode once --data-types user
python server/unified_scheduler_main.py --mode monitor
```

---

## 总结 Summary

统一数据调度器提供了完整的进程管理、监控和控制功能：

✅ **完整的运行模式**: monitor、once、status、stop、help
✅ **进程管理**: PID文件、状态文件、优雅停止
✅ **健康监控**: 自动健康检查、错误统计、资源监控
✅ **日志系统**: 结构化日志、日志轮转、错误追踪
✅ **配置管理**: 灵活的配置选项、数据类型选择
✅ **故障恢复**: 自动重启、错误处理、异常恢复

使用统一调度器可以大大简化系统管理，提高稳定性和可维护性。
