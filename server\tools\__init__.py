"""
Server Tools Module

Contains utility query for data collection and processing:
- Comment fetching
- Video information query
- Discussion utilities
- Cache management
"""

# Import available query
try:
    from .fetch_comments import fetch_single_dynamic_comments, fetch_single_video_comments
    COMMENT_TOOLS_AVAILABLE = True
except ImportError:
    COMMENT_TOOLS_AVAILABLE = False

try:
    from .discuss_utils import senta_sentences_sentiment_analysis
    DISCUSS_TOOLS_AVAILABLE = True
except ImportError:
    DISCUSS_TOOLS_AVAILABLE = False

try:
    from .cache_from_vtbs import *
    CACHE_TOOLS_AVAILABLE = True
except ImportError:
    CACHE_TOOLS_AVAILABLE = False

try:
    from .topic_fetch import *
    TOPIC_TOOLS_AVAILABLE = True
except ImportError:
    TOPIC_TOOLS_AVAILABLE = False

# Export available components
__all__ = []

if COMMENT_TOOLS_AVAILABLE:
    __all__.extend([
        "fetch_single_dynamic_comments",
        "fetch_single_video_comments"
    ])

if DISCUSS_TOOLS_AVAILABLE:
    __all__.extend([
        "senta_sentences_sentiment_analysis"
    ])
