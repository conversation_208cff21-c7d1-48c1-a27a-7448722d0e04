import asyncio
import requests
from utils.utils import read_vups_config
from const import PROJECT_ROOT
from logger import logger
from const import HEADERS

output_path = f"{PROJECT_ROOT}/frontend/public/vtubers"


def get_photo(save_id, url):
    response = requests.get(url)
    if response.status_code == 200:
        with open(f"{output_path}/{save_id}.jpg", "wb") as file:
            file.write(response.content)
        logger.info(f"Download picture success: {save_id}.jpg")
    else:
        logger.error(f"Download {save_id} picture failed: {response.status_code}")


def get_webinterface_by_mid(mid):
    """
    Get up's web interface by mid, tmp just photos
    """
    # https://github.com/SocialSisterYi/bilibili-API-collect/blob/master/docs/user/info.md#%E7%94%A8%E6%88%B7%E5%90%8D%E7%89%87%E4%BF%A1%E6%81%AF
    url = f"https://api.bilibili.com/x/web-interface/card?mid={mid}"
    response = requests.get(url, headers=HEADERS)
    data = response.json()
    if data["code"] == 0:
        return data["data"]["card"]["face"]
    else:
        logger.error(f"Get web interface failed: {data['message']}")
        return None

async def fetch_face_photo():
    vups = read_vups_config()
    for vup in vups:
        mid = str(vup["uid"])
        name = vup["shortName"]
        save_id = f"{name}"
        photo_url = get_webinterface_by_mid(mid)
        get_photo(save_id, photo_url)


async def main():
    await fetch_face_photo()

if __name__ == "__main__":
    asyncio.run(main())
