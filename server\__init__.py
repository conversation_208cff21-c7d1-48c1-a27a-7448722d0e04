"""
VupBI Server Package
VupBI服务器包

A modular data collection and scheduling system for VTuber analytics.
用于VTuber分析的模块化数据收集和调度系统。

This package provides:
- Creator information collection and scheduling
- User data collection and analysis  
- Live streaming data monitoring
- Unified data scheduling system
- Cookie management for authentication

Usage:
    # Import main server classes
    from vupbi.server import CreatorInfoServer, UserInfoServer
    
    # Import schedulers
    from vupbi.server import CreatorInfoScheduler, UnifiedDataScheduler
    
    # Import cookie management
    from vupbi.server import get_cookie_manager, get_task_cookie
"""

import sys
import os
from typing import Optional, Dict, Any

# Add current directory to path for internal imports
_current_dir = os.path.dirname(os.path.abspath(__file__))
if _current_dir not in sys.path:
    sys.path.insert(0, _current_dir)

# Version information
__version__ = "1.0.0"
__author__ = "VupBI Team"

# Package configuration
_config = {
    "initialized": False,
    "external_dependencies": {},
    "logger": None
}

def configure_package(
    logger=None,
    db_connection_factory=None,
    config_provider=None,
    **kwargs
):
    """
    Configure the server package with external dependencies
    配置服务器包的外部依赖
    
    Args:
        logger: Logger instance for the package
        db_connection_factory: Factory function for database connections
        config_provider: Configuration provider function
        **kwargs: Additional configuration options
    """
    global _config
    
    _config["logger"] = logger
    _config["external_dependencies"].update({
        "db_connection_factory": db_connection_factory,
        "config_provider": config_provider,
        **kwargs
    })
    _config["initialized"] = True

def get_package_config() -> Dict[str, Any]:
    """Get current package configuration"""
    return _config.copy()

def is_configured() -> bool:
    """Check if package is properly configured"""
    return _config["initialized"]

# Lazy imports to avoid circular dependencies
def _get_creator_info_server():
    """Lazy import for CreatorInfoServer"""
    try:
        from .gate.creator_info_server import CreatorInfoServer
        return CreatorInfoServer
    except ImportError as e:
        if _config["logger"]:
            _config["logger"].warning(f"CreatorInfoServer not available: {e}")
        return None

def _get_creator_info_scheduler():
    """Lazy import for CreatorInfoScheduler"""
    try:
        from .gate.creator_info_server import CreatorInfoScheduler
        return CreatorInfoScheduler
    except ImportError as e:
        if _config["logger"]:
            _config["logger"].warning(f"CreatorInfoScheduler not available: {e}")
        return None

def _get_user_info_server():
    """Lazy import for UserInfoServer"""
    try:
        from .gate.user_info_till_server import UserInfoServer
        return UserInfoServer
    except ImportError as e:
        if _config["logger"]:
            _config["logger"].warning(f"UserInfoServer not available: {e}")
        return None

def _get_unified_scheduler():
    """Lazy import for UnifiedDataScheduler"""
    try:
        from .unified_data_scheduler import UnifiedDataScheduler
        return UnifiedDataScheduler
    except ImportError as e:
        if _config["logger"]:
            _config["logger"].warning(f"UnifiedDataScheduler not available: {e}")
        return None

def _get_cookie_manager():
    """Lazy import for cookie manager"""
    try:
        from .base.cookie_manager import get_cookie_manager
        return get_cookie_manager
    except ImportError as e:
        if _config["logger"]:
            _config["logger"].warning(f"Cookie manager not available: {e}")
        return None

def _get_task_cookie():
    """Lazy import for task cookie function"""
    try:
        from .base.cookie_manager import get_task_cookie
        return get_task_cookie
    except ImportError as e:
        if _config["logger"]:
            _config["logger"].warning(f"Task cookie function not available: {e}")
        return None

# Public API - these will be available when importing the package
__all__ = [
    "configure_package",
    "get_package_config", 
    "is_configured",
    "CreatorInfoServer",
    "CreatorInfoScheduler", 
    "UserInfoServer",
    "UnifiedDataScheduler",
    "get_cookie_manager",
    "get_task_cookie",
    "__version__"
]

# Create lazy properties for main classes
class _LazyLoader:
    """Lazy loader for package components"""
    
    @property
    def CreatorInfoServer(self):
        cls = _get_creator_info_server()
        if cls is None:
            raise ImportError("CreatorInfoServer is not available")
        return cls
    
    @property 
    def CreatorInfoScheduler(self):
        cls = _get_creator_info_scheduler()
        if cls is None:
            raise ImportError("CreatorInfoScheduler is not available")
        return cls
        
    @property
    def UserInfoServer(self):
        cls = _get_user_info_server()
        if cls is None:
            raise ImportError("UserInfoServer is not available")
        return cls
        
    @property
    def UnifiedDataScheduler(self):
        cls = _get_unified_scheduler()
        if cls is None:
            raise ImportError("UnifiedDataScheduler is not available")
        return cls
        
    @property
    def get_cookie_manager(self):
        func = _get_cookie_manager()
        if func is None:
            raise ImportError("Cookie manager is not available")
        return func
        
    @property
    def get_task_cookie(self):
        func = _get_task_cookie()
        if func is None:
            raise ImportError("Task cookie function is not available")
        return func

# Create lazy loader instance
_loader = _LazyLoader()

# Export lazy-loaded components
CreatorInfoServer = _loader.CreatorInfoServer
CreatorInfoScheduler = _loader.CreatorInfoScheduler
UserInfoServer = _loader.UserInfoServer
UnifiedDataScheduler = _loader.UnifiedDataScheduler
get_cookie_manager = _loader.get_cookie_manager
get_task_cookie = _loader.get_task_cookie
