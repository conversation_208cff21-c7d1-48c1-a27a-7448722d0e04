#!/usr/bin/env python3
"""
Creator Info Server
Creator Info Server Scheduler Main Program

Scheduled task system for creator_info_server.py based on till_main.py design pattern

Usage:
    python creator_info_scheduler.py
"""

import multiprocessing
import asyncio
import time
import signal
import sys
from datetime import datetime
from .gate.creator_info_server import CreatorInfoScheduler

try:
    from logger import logger
except ImportError:
    from server.base.config import get_logger
    logger = get_logger()

try:
    from .base.cookie_manager import get_cookie_field
except ImportError:
    get_cookie_field = None
from bilibili_api import Credential


class CreatorInfoSchedulerManager:
    """
    Creator Info 调度器管理器
    Manager for Creator Info Scheduler
    """
    
    def __init__(self, uid="401315430", daily_hour=2, daily_minute=0):
        self.uid = uid
        self.daily_hour = daily_hour
        self.daily_minute = daily_minute
        self.scheduler = None
        self.is_running = False
        
        # 创建凭证
        self.credential = Credential(
            sessdata=get_cookie_field("creator", "SESSDATA"),
            bili_jct=get_cookie_field("creator", "bili_jct"),
            buvid3=get_cookie_field("creator", "buvid3"),
            dedeuserid=get_cookie_field("creator", "DedeUserID"),
            buvid4=get_cookie_field("creator", "buvid4"),
        )
    
    async def initialize_and_start(self):
        """初始化并启动调度器"""
        try:
            logger.info(f"Initializing Creator Info Scheduler for UID: {self.uid}")
            
            # 创建调度器实例
            self.scheduler = CreatorInfoScheduler(uid=self.uid, credential=self.credential)
            
            # 初始化
            await self.scheduler.initialize()
            
            # 启动调度器
            self.scheduler.start_scheduler(
                daily_hour=self.daily_hour,
                daily_minute=self.daily_minute
            )
            
            self.is_running = True
            logger.info("Creator Info Scheduler started successfully")
            
            # 保持运行
            while self.is_running:
                await asyncio.sleep(60)  # 每分钟检查一次
                
        except Exception as e:
            logger.error(f"Error in scheduler manager: {e}")
            raise
    
    def stop(self):
        """停止调度器"""
        if self.scheduler:
            self.scheduler.stop_scheduler()
        self.is_running = False
        logger.info("Creator Info Scheduler Manager stopped")
    
    def get_status(self):
        """获取状态"""
        if self.scheduler:
            return self.scheduler.get_scheduler_status()
        return {"status": "not_initialized", "jobs": []}


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)


async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建管理器
    manager = CreatorInfoSchedulerManager()
    
    try:
        await manager.initialize_and_start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
    finally:
        manager.stop()


def worker():
    """工作进程"""
    while True:
        try:
            asyncio.run(main())
        except Exception as e:
            logger.error(f"Worker encountered an error: {e}")
            time.sleep(5)


def monitor():
    """监控进程 - 负责重启工作进程"""
    logger.info("Starting Creator Info Scheduler Monitor")
    
    while True:
        try:
            p = multiprocessing.Process(target=worker)
            p.start()
            logger.info(f"Started worker process with PID: {p.pid}")
            
            while p.is_alive():
                p.join(timeout=10)
            
            logger.warning("Worker process ended, restarting...")
            time.sleep(5)
            
        except KeyboardInterrupt:
            logger.info("Monitor received keyboard interrupt, shutting down...")
            if 'p' in locals() and p.is_alive():
                p.terminate()
                p.join()
            break
        except Exception as e:
            logger.error(f"Monitor encountered an error: {e}")
            time.sleep(10)


def run_once():
    """运行一次所有任务（用于测试）"""
    async def test_run():
        manager = CreatorInfoSchedulerManager()
        await manager.initialize_and_start()
        
        # 执行一次所有函数
        if manager.scheduler:
            await manager.scheduler.execute_all_functions()
        
        manager.stop()
    
    asyncio.run(test_run())


def show_status():
    """显示调度器状态（需要实现状态查询接口）"""
    print("Status query functionality would be implemented here")
    print("This would connect to a running scheduler instance to get status")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Creator Info Scheduler')
    parser.add_argument('--mode', choices=['monitor', 'once', 'status'], 
                       default='monitor', help='运行模式')
    parser.add_argument('--uid', default='401315430', help='用户UID')
    parser.add_argument('--hour', type=int, default=2, help='每日执行小时')
    parser.add_argument('--minute', type=int, default=0, help='每日执行分钟')
    
    args = parser.parse_args()
    
    if args.mode == 'monitor':
        logger.info("Starting in monitor mode...")
        monitor()
    elif args.mode == 'once':
        logger.info("Running once...")
        run_once()
    elif args.mode == 'status':
        show_status()
