"""
Creator Info Scheduler 配置文件
Configuration file for Creator Info Scheduler
"""

# 调度配置
SCHEDULER_CONFIG = {
    # 默认执行时间
    'default_daily_hour': 2,
    'default_daily_minute': 0,
    
    # 重试配置
    'max_retries': 3,
    'retry_delay': 5,  # 秒
    
    # 函数执行间隔
    'function_delay': 5,  # 函数间延迟（秒）
    'category_delay': 10,  # 分类间延迟（秒）
    
    # 监控配置
    'monitor_check_interval': 60,  # 监控检查间隔（秒）
    'worker_restart_delay': 5,  # 工作进程重启延迟（秒）
}

BUSINESS_FUNCTIONS_CONFIG = {
    'overview': {
        'functions': [
            'fetch_overview_stat',
            'fetch_attention_analyze', 
            'fetch_archive_analyze',
            'fetch_video_overview'
        ],
        'description': '概览数据收集',
        'priority': 1,
        'enabled': True
    },
    'fans': {
        'functions': [
            'fetch_fan_graph',
            'fetch_fan_overview'
        ],
        'description': '粉丝数据收集',
        'priority': 2,
        'enabled': True
    },
    'video': {
        'functions': [
            'fetch_video_compare',
            'fetch_video_pandect',
            'fetch_video_survey',
            'fetch_video_source',
            'fetch_video_view_data'
        ],
        'description': '视频数据收集',
        'priority': 3,
        'enabled': True
    }
}

SPECIAL_FUNCTIONS_CONFIG = {
    'fetch_archive_analyze': {
        'params': {'period': 0},
        'description': '播放分析，默认周期为0'
    },
    'fetch_video_compare': {
        'params': {'size': 1000},
        'description': '视频比较，默认获取1000条'
    },
    'fetch_video_survey': {
        'params': {'data_type': 1},
        'description': '视频调查，默认类型为播放数据'
    }
}

LOGGING_CONFIG = {
    'log_level': 'INFO',
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'log_file': 'logs/creator_scheduler.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

DATABASE_CONFIG = {
    'connection_timeout': 30,
    'command_timeout': 60,
    'max_retries': 3,
    'retry_delay': 3
}

HTTP_CONFIG = {
    'max_retries': 3,
    'retry_delay': 5,
    'timeout': 30,
    'headers': {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
}

SCHEDULE_JOBS_CONFIG = {
    'daily_full_collection': {
        'hour': 2,
        'minute': 0,
        'description': '每日完整数据收集',
        'enabled': True
    },
    'hourly_overview': {
        'minute': 0,
        'description': '每小时概览数据收集（可选）',
        'enabled': False,
        'functions': ['fetch_overview_stat']
    }
}

def get_enabled_functions():
    """获取启用的业务函数列表"""
    enabled_functions = {}
    for category, config in BUSINESS_FUNCTIONS_CONFIG.items():
        if config.get('enabled', True):
            enabled_functions[category] = config['functions']
    return enabled_functions

def get_function_params(func_name):
    """获取函数的特殊参数"""
    return SPECIAL_FUNCTIONS_CONFIG.get(func_name, {}).get('params', {})

def get_scheduler_config():
    """获取调度器配置"""
    return SCHEDULER_CONFIG.copy()

def get_logging_config():
    """获取日志配置"""
    return LOGGING_CONFIG.copy()
