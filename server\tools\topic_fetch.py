import requests
import json
import time
from datetime import datetime
from collections import Counter
import os
# Import cookie manager for getting cookie values
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent / "server"))
from cookie_manager import get_sessdata, get_buvid3

# API URL template
API_URL = "https://api.bilibili.com/x/polymer/web-dynamic/v1/feed/topic"

# Request parameters
PARAMS = {
    "topic_id": 66066,
    "sort_by": 2,
    "page_size": 20,
    "source": "Web"
}

# Headers to mimic browser request with cookies
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Referer": "https://www.bilibili.com/",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Cookie": f"SESSDATA={get_sessdata('user')}; buvid3={get_buvid3('user')}"
}

def fetch_data(offset=""):
    """
    Fetch data from Bilibili API
    
    Args:
        offset (str): Offset parameter for pagination
        
    Returns:
        dict: API response data
    """
    params = PARAMS.copy()
    params["offset"] = offset
    
    try:
        response = requests.get(API_URL, params=params, headers=HEADERS, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None

def save_data_to_file(data, offset):
    """
    Save data to local file with timestamp
    
    Args:
        data (dict): Data to save
        offset (str): Current offset value
    """
    # Create output/topic directory if it doesn't exist
    output_dir = "output/topic2"
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{output_dir}/bilibili_data_{timestamp}_offset_{offset}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")

def extract_user_names(data):
    """
    Extract user names from API response
    
    Args:
        data (dict): API response data
        
    Returns:
        list: List of user names
    """
    names = []
    try:
        items = data.get("data", {}).get("topic_card_list", {}).get("items", [])
        for item in items:
            dynamic_card = item.get("dynamic_card_item", {})
            modules = dynamic_card.get("modules", {})
            author = modules.get("module_author", {})
            name = author.get("name", "")
            if name:
                names.append(name)
    except Exception as e:
        print(f"提取用户名失败: {e}")
    
    return names

def extract_interaction_stats(data):
    """
    Extract interaction statistics (like, comment, forward) from API response
    
    Args:
        data (dict): API response data
        
    Returns:
        list: List of tuples (username, total_interactions)
    """
    stats = []
    try:
        items = data.get("data", {}).get("topic_card_list", {}).get("items", [])
        for item in items:
            dynamic_card = item.get("dynamic_card_item", {})
            modules = dynamic_card.get("modules", {})
            
            # Get username
            author = modules.get("module_author", {})
            username = author.get("name", "")
            
            # Get interaction stats
            module_stat = modules.get("module_stat", {})
            like_count = module_stat.get("like", {}).get("count", 0)
            comment_count = module_stat.get("comment", {}).get("count", 0)
            forward_count = module_stat.get("forward", {}).get("count", 0)
            
            total_interactions = like_count + comment_count + forward_count
            
            if username:
                stats.append((username, total_interactions, like_count, comment_count, forward_count))
    except Exception as e:
        print(f"提取互动数据失败: {e}")
    
    return stats

def analyze_data(all_names, all_stats):
    """
    Analyze collected data and print results
    
    Args:
        all_names (list): All collected usernames
        all_stats (list): All collected interaction statistics
    """
    print("\n" + "="*50)
    print("数据分析结果")
    print("="*50)
    
    # Task 1: Top 10 most frequent usernames
    print("\n1. 出现最多的用户名前十:")
    name_counter = Counter(all_names)
    top_names = name_counter.most_common(10)
    
    for i, (name, count) in enumerate(top_names, 1):
        print(f"   {i:2d}. {name}: {count}次")
    
    # Task 2: Top 10 users with highest interactions (separated by type)
    print("\n2. 互动数据最高的前十个用户:")
    
    # 2.1 Top 10 users with highest likes
    print("\n   2.1 点赞数最高的前十个用户:")
    sorted_by_likes = sorted(all_stats, key=lambda x: x[2], reverse=True)  # x[2] is like_count
    top_likes = sorted_by_likes[:10]
    
    for i, (username, total, likes, comments, forwards) in enumerate(top_likes, 1):
        print(f"      {i:2d}. {username}: 点赞{likes} (评论:{comments}, 转发:{forwards}, 总计:{total})")
    
    # 2.2 Top 10 users with highest comments
    print("\n   2.2 评论数最高的前十个用户:")
    sorted_by_comments = sorted(all_stats, key=lambda x: x[3], reverse=True)  # x[3] is comment_count
    top_comments = sorted_by_comments[:10]
    
    for i, (username, total, likes, comments, forwards) in enumerate(top_comments, 1):
        print(f"      {i:2d}. {username}: 评论{comments} (点赞:{likes}, 转发:{forwards}, 总计:{total})")
    
    # 2.3 Top 10 users with highest forwards
    print("\n   2.3 转发数最高的前十个用户:")
    sorted_by_forwards = sorted(all_stats, key=lambda x: x[4], reverse=True)  # x[4] is forward_count
    top_forwards = sorted_by_forwards[:10]
    
    for i, (username, total, likes, comments, forwards) in enumerate(top_forwards, 1):
        print(f"      {i:2d}. {username}: 转发{forwards} (点赞:{likes}, 评论:{comments}, 总计:{total})")
    
    # 2.4 Top 10 users with highest total interactions
    print("\n   2.4 总互动数最高的前十个用户:")
    sorted_stats = sorted(all_stats, key=lambda x: x[1], reverse=True)  # x[1] is total_interactions
    top_interactions = sorted_stats[:10]
    
    for i, (username, total, likes, comments, forwards) in enumerate(top_interactions, 1):
        print(f"      {i:2d}. {username}: 总计{total} (点赞:{likes}, 评论:{comments}, 转发:{forwards})")

def main():
    """
    Main function to orchestrate the data collection and analysis
    """
    print("开始循环拉取B站话题数据...")
    print(f"话题ID: {PARAMS['topic_id']}")
    print(f"每页大小: {PARAMS['page_size']}")
    print("-" * 50)
    
    offset = ""
    page_count = 0
    all_names = []
    all_stats = []
    
    try:
        while True:
            page_count += 1
            print(f"\n正在拉取第 {page_count} 页数据 (offset: {offset})...")
            
            # Fetch data
            data = fetch_data(offset)
            if not data:
                print("获取数据失败，停止循环")
                break
            
            # Check if request was successful
            if data.get("code") != 0:
                print(f"API返回错误: {data.get('message', 'Unknown error')}")
                break
            
            # Save data to file
            save_data_to_file(data, offset)
            
            # Extract data for analysis
            names = extract_user_names(data)
            stats = extract_interaction_stats(data)
            
            all_names.extend(names)
            all_stats.extend(stats)
            
            print(f"本页获取到 {len(names)} 个用户")
            
            # Get next offset
            topic_card_list = data.get("data", {}).get("topic_card_list", {})
            next_offset = topic_card_list.get("offset", "")
            has_more = topic_card_list.get("has_more", False)
            
            if not has_more or not next_offset:
                print("没有更多数据，停止循环")
                break
            
            offset = next_offset
            
            # Delay 5 seconds before next request
            print("等待5秒后继续...")
            time.sleep(1)
            
            # # Optional: limit pages for testing (remove this in production)
            # if page_count >= 10:  # Limit to 10 pages for testing
            #     print("已达到测试页数限制，停止循环")
            #     break
                
    except KeyboardInterrupt:
        print("\n用户中断，停止数据收集")
    except Exception as e:
        print(f"发生错误: {e}")
    
    # Analyze collected data
    if all_names and all_stats:
        analyze_data(all_names, all_stats)
    else:
        print("没有收集到数据进行分析")
    
    print(f"\n数据收集完成，共处理 {page_count} 页数据")
    print(f"总共收集到 {len(all_names)} 个用户名")
    print(f"总共收集到 {len(all_stats)} 条互动数据")

def analyze_all_json_files():
    """
    统计output/topic目录中所有json文件的数据
    完成两个查询任务：
    1. dynamic_card_item.modules.module_author.name 出现最多的名字及次数（前十名）
    2. dynamic_card_item.modules.module_stat 三个值累计最大的用户（前十名）
    """
    print("开始分析output/topic目录中的所有json文件...")
    
    # 检查目录是否存在
    topic_dir = "output/topic"
    if not os.path.exists(topic_dir):
        print(f"目录 {topic_dir} 不存在")
        return
    
    # 获取所有json文件
    json_files = [f for f in os.listdir(topic_dir) if f.endswith('.json')]
    if not json_files:
        print(f"目录 {topic_dir} 中没有找到json文件")
        return
    
    print(f"找到 {len(json_files)} 个json文件")
    
    # 用于统计的数据结构
    name_counter = Counter()  # 统计用户名出现次数
    user_stats = {}  # 统计每个用户的互动数据 {username: {'like': total, 'comment': total, 'forward': total}}
    
    processed_files = 0
    total_items = 0
    
    # 遍历所有json文件
    for filename in json_files:
        filepath = os.path.join(topic_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查数据结构
            if data.get("code") != 0:
                print(f"文件 {filename} 中的API返回错误: {data.get('message', 'Unknown error')}")
                continue
            
            # 获取动态列表
            items = data.get("data", {}).get("topic_card_list", {}).get("items", [])
            
            for item in items:
                dynamic_card = item.get("dynamic_card_item", {})
                modules = dynamic_card.get("modules", {})
                
                # 提取用户名
                module_author = modules.get("module_author", {})
                username = module_author.get("name", "")
                
                if username:
                    # 统计用户名出现次数
                    name_counter[username] += 1
                    
                    # 提取互动统计数据
                    module_stat = modules.get("module_stat", {})
                    like_count = module_stat.get("like", {}).get("count", 0)
                    comment_count = module_stat.get("comment", {}).get("count", 0)
                    forward_count = module_stat.get("forward", {}).get("count", 0)
                    
                    # 累计用户的互动数据
                    if username not in user_stats:
                        user_stats[username] = {'like': 0, 'comment': 0, 'forward': 0, 'total': 0}
                    
                    user_stats[username]['like'] += like_count
                    user_stats[username]['comment'] += comment_count
                    user_stats[username]['forward'] += forward_count
                    user_stats[username]['total'] += (like_count + comment_count + forward_count)
                
                total_items += 1
            
            processed_files += 1
            
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {e}")
            continue
    
    print(f"成功处理 {processed_files} 个文件，共 {total_items} 条动态数据")
    print("="*80)
    
    # 任务1：统计用户名出现最多的前十名
    print("任务1：dynamic_card_item.modules.module_author.name 出现最多的前十名")
    print("-"*60)
    top_names = name_counter.most_common(10)
    
    for i, (name, count) in enumerate(top_names, 1):
        print(f"{i:2d}. {name}: {count}次")
    
    print("\n" + "="*80)
    
    # 任务2：按用户累计互动数据排序（分别按点赞、评论、转发、总计排序）
    print("任务2：dynamic_card_item.modules.module_stat 累计数据最高的前十名用户")
    print("-"*60)
    
    # 2.1 按点赞数排序
    print("\n2.1 点赞数累计最高的前十名用户:")
    sorted_by_likes = sorted(user_stats.items(), key=lambda x: x[1]['like'], reverse=True)[:10]
    for i, (username, stats) in enumerate(sorted_by_likes, 1):
        print(f"{i:2d}. {username}: 点赞{stats['like']} (评论:{stats['comment']}, 转发:{stats['forward']}, 总计:{stats['total']})")
    
    # 2.2 按评论数排序
    print("\n2.2 评论数累计最高的前十名用户:")
    sorted_by_comments = sorted(user_stats.items(), key=lambda x: x[1]['comment'], reverse=True)[:10]
    for i, (username, stats) in enumerate(sorted_by_comments, 1):
        print(f"{i:2d}. {username}: 评论{stats['comment']} (点赞:{stats['like']}, 转发:{stats['forward']}, 总计:{stats['total']})")
    
    # 2.3 按转发数排序
    print("\n2.3 转发数累计最高的前十名用户:")
    sorted_by_forwards = sorted(user_stats.items(), key=lambda x: x[1]['forward'], reverse=True)[:10]
    for i, (username, stats) in enumerate(sorted_by_forwards, 1):
        print(f"{i:2d}. {username}: 转发{stats['forward']} (点赞:{stats['like']}, 评论:{stats['comment']}, 总计:{stats['total']})")
    
    # 2.4 按总互动数排序
    print("\n2.4 总互动数累计最高的前十名用户:")
    sorted_by_total = sorted(user_stats.items(), key=lambda x: x[1]['total'], reverse=True)[:10]
    for i, (username, stats) in enumerate(sorted_by_total, 1):
        print(f"{i:2d}. {username}: 总计{stats['total']} (点赞:{stats['like']}, 评论:{stats['comment']}, 转发:{stats['forward']})")
    
    print("\n" + "="*80)
    print("数据分析完成！")


if __name__ == "__main__":
    # # 可以选择运行原有的数据收集功能或新的数据分析功能
    # import sys
    
    # if len(sys.argv) > 1 and sys.argv[1] == "analyze":
    #     # 运行数据分析功能
    #     analyze_all_json_files()
    # else:
    #     # 运行原有的数据收集功能
    #     main()
    analyze_all_json_files()
