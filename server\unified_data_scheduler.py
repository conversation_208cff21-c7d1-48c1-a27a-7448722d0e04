"""
Unified Data Scheduler
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

try:
    from aiolimiter import AsyncLimiter
    AIOLIMITER_AVAILABLE = True
except ImportError:
    AIOLIMITER_AVAILABLE = False

try:
    from logger import logger
except ImportError:
    from server.base.config import get_logger
    logger = get_logger()

# Warn about aiolimiter after logger is available
if not AIOLIMITER_AVAILABLE:
    logger.warning("aiolimiter not available, rate limiting will be disabled")

from server.base.unified_scheduler_config import (
    load_unified_config,
    get_enabled_data_types,
    get_data_type_functions,
    get_data_type_schedule,
    get_scheduler_config
)

# Import existing servers (with optional imports)
try:
    from server.gate.creator_info_server import CreatorInfoScheduler
    CREATOR_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Creator server not available: {e}")
    CREATOR_AVAILABLE = False
    CreatorInfoScheduler = None

try:
    from server.gate.user_info_till_server import _ensure_vtuber_servers
    USER_SERVER_AVAILABLE = True
except ImportError as e:
    logger.warning(f"User server not available: {e}")
    USER_SERVER_AVAILABLE = False
    _ensure_vtuber_servers = lambda: {}

try:
    from server.gate.live_info_till_server import MyHandler, execute_once as live_execute_once, execute_monitor as live_execute_monitor
    LIVE_HANDLER_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Live handler not available: {e}")
    LIVE_HANDLER_AVAILABLE = False
    MyHandler = None
    live_execute_once = None
    live_execute_monitor = None

import aiohttp

try:
    from .base.cookie_manager import get_cookie_manager, get_task_cookie
    COOKIE_MANAGER_AVAILABLE = True
except ImportError:
    COOKIE_MANAGER_AVAILABLE = False
    get_cookie_manager = None
    get_task_cookie = None
    logger.warning("Cookie manager not available, cookie functionality will be disabled")


class UnifiedDataScheduler:
    """
    Unified Data Scheduler - 统一数据调度器
    Integrates all polling systems (user, creator, live data)
    """

    def __init__(self, data_types: List[str] = None, uid: str = "401315430", credential=None):
        """
        Initialize unified scheduler
        
        Args:
            data_types: List of data types to enable ['user', 'creator', 'live', 'all']
            uid: User ID for creator data
            credential: Bilibili credential for API access
        """
        self.uid = uid
        self.credential = credential
        self.scheduler = None
        self.is_running = False
        
        # Load configuration
        self.config = load_unified_config()
        self.scheduler_config = get_scheduler_config()
        
        # Determine enabled data types
        if data_types is None:
            self.enabled_data_types = get_enabled_data_types()
        elif 'all' in data_types:
            self.enabled_data_types = ['user', 'creator', 'live']
        else:
            self.enabled_data_types = data_types
        
        # Initialize components
        self.creator_scheduler = None
        self.user_servers = None
        self.live_handler = None
        self.live_session = None

        # Task tracking for advanced scheduling
        self.active_main_tasks_count = 0
        self.main_task_names = set()

        # Initialize rate limiters if available
        self._init_rate_limiters()

        # Initialize cookie manager if available
        self._init_cookie_manager()

        logger.info(f"UnifiedDataScheduler initialized with data types: {self.enabled_data_types}")

    def _init_cookie_manager(self):
        """Initialize cookie manager if available"""
        if COOKIE_MANAGER_AVAILABLE:
            try:
                self.cookie_manager = get_cookie_manager()
                logger.info("Cookie manager initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize cookie manager: {e}")
                self.cookie_manager = None
        else:
            self.cookie_manager = None
            logger.info("Cookie manager not available, skipping initialization")

    def _init_rate_limiters(self):
        """Initialize rate limiters for different task types"""
        if not AIOLIMITER_AVAILABLE:
            self.limiters = {}
            return

        user_config = self.config.get("data_types", {}).get("user", {})
        limiter_config = user_config.get("limiters", {})

        self.limiters = {
            "hourly_task": AsyncLimiter(limiter_config.get("hourly_task_limit", 1), 1),
            "daily_task": AsyncLimiter(limiter_config.get("daily_task_limit", 1), 1),
            "long_running_task": AsyncLimiter(limiter_config.get("long_running_task_limit", 1), 1),
            "till_worker": AsyncLimiter(limiter_config.get("till_worker_limit", 1), 1)
        }

    async def _execute_task_wrapper(self, task_func, task_name: str, *args, **kwargs):
        """
        Task execution wrapper with error handling and logging
        任务执行包装器，包含错误处理和日志记录
        """
        is_main_task = task_name in self.main_task_names
        if is_main_task:
            self.active_main_tasks_count += 1
            logger.info(f"Scheduler triggered main task: {task_name}, active main tasks: {self.active_main_tasks_count}")
        else:
            logger.info(f"Scheduler triggered task: {task_name}")

        try:
            result = await task_func(task_name, *args, **kwargs)
            logger.info(f"Task {task_name} completed successfully. Result: {result}")
            return result
        except asyncio.CancelledError:
            logger.warning(f"Task {task_name} was cancelled.")
            raise
        except Exception as e:
            logger.error(f"Task {task_name} failed with uncaught exception: {e}", exc_info=True)
            raise
        finally:
            if is_main_task:
                self.active_main_tasks_count -= 1
                logger.info(f"Main task {task_name} finished, active main tasks: {self.active_main_tasks_count}")

    def _add_task_to_scheduler(self, task_func, task_name: str, trigger_type: str, **trigger_args):
        """
        Add task to scheduler with advanced options
        添加任务到调度器，支持高级选项
        """
        # Mark non-idle tasks as main tasks
        if task_name != "空闲时间补充任务":
            self.main_task_names.add(task_name)

        if trigger_type == "cron":
            jitter_value = trigger_args.pop("jitter", None)
            trigger = CronTrigger(
                **trigger_args,
                timezone=self.scheduler_config.get('timezone', 'Asia/Shanghai'),
                jitter=jitter_value,
            )
        elif trigger_type == "interval":
            jitter_value = trigger_args.pop("jitter", None)
            trigger = IntervalTrigger(
                **trigger_args,
                timezone=self.scheduler_config.get('timezone', 'Asia/Shanghai'),
                jitter=jitter_value,
            )
        else:
            logger.error(f"Unsupported trigger type: {trigger_type}, task {task_name} not added.")
            return

        self.scheduler.add_job(
            self._execute_task_wrapper,
            trigger=trigger,
            args=[task_func, task_name],
            id=task_name,
            name=task_name,
            replace_existing=True,
            misfire_grace_time=300,  # Allow 5 minutes grace time for missed executions
            next_run_time=datetime.now() + timedelta(seconds=1),  # Start immediately
        )
        logger.info(f"Task {task_name} added to scheduler. Trigger: {trigger_type}, args: {trigger_args}")

    async def initialize(self):
        """Initialize all required components"""
        try:
            # Start cookie auto-refresh if available
            if self.cookie_manager:
                try:
                    self.cookie_manager.start_auto_refresh()
                    logger.info("Cookie auto-refresh started")
                except Exception as e:
                    logger.warning(f"Failed to start cookie auto-refresh: {e}")
            else:
                logger.info("Cookie manager not available, skipping auto-refresh")

            # Initialize creator scheduler if enabled
            if 'creator' in self.enabled_data_types:
                await self._initialize_creator_scheduler()

            # Initialize user servers if enabled
            if 'user' in self.enabled_data_types:
                await self._initialize_user_servers()

            # Initialize live monitoring if enabled
            if 'live' in self.enabled_data_types:
                await self._initialize_live_monitoring()

            logger.info("UnifiedDataScheduler initialization completed")

        except Exception as e:
            logger.error(f"Error during UnifiedDataScheduler initialization: {e}")
            raise

    async def _initialize_creator_scheduler(self):
        """Initialize creator data scheduler"""
        if not CREATOR_AVAILABLE:
            logger.warning("Creator scheduler not available, skipping initialization")
            return

        try:
            self.creator_scheduler = CreatorInfoScheduler(uid=self.uid, credential=self.credential)
            await self.creator_scheduler.initialize()
            logger.info("Creator scheduler initialized")
        except Exception as e:
            logger.error(f"Error initializing creator scheduler: {e}")
            raise

    async def _initialize_user_servers(self):
        """Initialize user data servers"""
        if not USER_SERVER_AVAILABLE:
            logger.warning("User servers not available, skipping initialization")
            return

        try:
            self.user_servers = _ensure_vtuber_servers()

            # Initialize all user servers
            for _, server in self.user_servers.items():
                if server.liveid is None:
                    await server.async_init()
            logger.info(f"User servers initialized for {len(self.user_servers)} vtubers")
        except Exception as e:
            logger.error(f"Error initializing user servers: {e}")
            raise

    async def _initialize_live_monitoring(self):
        """Initialize live monitoring components"""
        if not LIVE_HANDLER_AVAILABLE:
            logger.warning("Live handler not available, skipping initialization")
            return

        try:
            # Initialize session for live monitoring
            self.live_session = aiohttp.ClientSession()

            # Initialize live handler
            self.live_handler = MyHandler()
            logger.info("Live monitoring initialized")
        except Exception as e:
            logger.error(f"Error initializing live monitoring: {e}")
            raise

    async def _get_rate_limiter(self, frequency: str):
        """Get appropriate rate limiter for frequency"""
        if not AIOLIMITER_AVAILABLE or not self.limiters:
            return None

        limiter_map = {
            "hourly": "hourly_task",
            "daily": "daily_task",
            "three_day": "long_running_task",
            "weekly": "long_running_task",
            "monthly": "long_running_task",
            "till": "till_worker"
        }

        limiter_key = limiter_map.get(frequency, "long_running_task")
        return self.limiters.get(limiter_key)

    async def _execute_user_worker_with_limiter(self, task_name: str, frequency: str):
        """Execute user worker with rate limiting"""
        limiter = await self._get_rate_limiter(frequency)

        if limiter:
            async with limiter:
                logger.info(f"Rate limiter allowed task: {task_name} to execute")
                return await self._execute_user_worker_by_frequency(frequency)
        else:
            return await self._execute_user_worker_by_frequency(frequency)

    async def _execute_user_worker_by_frequency(self, frequency: str):
        """Execute user worker based on frequency using existing worker functions"""
        if 'user' not in self.enabled_data_types or not self.user_servers:
            return

        if not USER_SERVER_AVAILABLE:
            logger.warning(f"User server not available, skipping {frequency} worker")
            return

        # Import worker functions from user_info_till_server
        try:
            from server.gate.user_info_till_server import (
                user_info_hour_worker,
                user_info_1_day_worker,
                user_info_3_day_worker,
                user_info_week_worker,
                user_info_month_worker,
                user_info_till_worker
            )

            worker_map = {
                "hourly": user_info_hour_worker,
                "daily": user_info_1_day_worker,
                "three_day": user_info_3_day_worker,
                "weekly": user_info_week_worker,
                "monthly": user_info_month_worker,
                "till": user_info_till_worker
            }

            worker_func = worker_map.get(frequency)
            if worker_func:
                logger.info(f"Executing user {frequency} worker")
                await worker_func()
                logger.info(f"User {frequency} worker completed")
            else:
                logger.error(f"Unknown frequency: {frequency}")

        except ImportError as e:
            logger.error(f"Failed to import user worker functions: {e}")
        except Exception as e:
            logger.error(f"Error executing user {frequency} worker: {e}")

    async def run_idle_task_if_needed(self, task_name: str):
        """
        Run till worker if scheduler is idle (no main tasks running)
        如果调度器空闲则运行till worker
        """
        active_count = self.active_main_tasks_count
        if active_count == 0:
            logger.info(f"Scheduler is idle, executing {task_name} (till worker).")
            try:
                await self._execute_user_worker_with_limiter(task_name, "till")
                logger.info(f"{task_name} (till worker) executed successfully.")
            except Exception as e:
                logger.error(f"{task_name} (till worker) failed: {e}", exc_info=True)
        else:
            logger.info(f"Scheduler has {active_count} main tasks running, skipping {task_name}.")

    async def execute_user_functions_safely(self, frequency: str):
        """
        Execute user data functions safely with advanced scheduling
        安全执行用户数据函数（高级调度）
        """
        task_name = f"用户数据{frequency}任务"
        return await self._execute_user_worker_with_limiter(task_name, frequency)

    async def execute_creator_functions_safely(self):
        """Execute creator data functions safely"""
        if 'creator' not in self.enabled_data_types or not self.creator_scheduler:
            return
        
        try:
            logger.info("Starting creator data collection")
            await self.creator_scheduler.execute_all_functions()
            logger.info("Creator data collection completed")
        except Exception as e:
            logger.error(f"Error in creator data collection: {e}")

    async def execute_live_monitoring(self, mode: str = "once"):
        """
        Execute live monitoring tasks

        Args:
            mode: "once" for single execution, "monitor" for continuous monitoring
        """
        if 'live' not in self.enabled_data_types:
            logger.info("Live monitoring disabled, skipping")
            return

        if not LIVE_HANDLER_AVAILABLE:
            logger.warning("Live handler not available, skipping live monitoring")
            return

        try:
            logger.info(f"Starting live monitoring in {mode} mode")

            if mode == "once":
                # Execute once for testing
                await live_execute_once()
                logger.info("Live monitoring once execution completed")
            elif mode == "monitor":
                # Execute continuous monitoring
                await live_execute_monitor()
                logger.info("Live monitoring continuous execution completed")
            else:
                logger.error(f"Unknown live monitoring mode: {mode}")

        except Exception as e:
            logger.error(f"Error in live monitoring ({mode} mode): {e}")
            raise

    def start_scheduler(self):
        """Start the unified scheduler"""
        if self.is_running:
            logger.warning("Unified scheduler is already running")
            return
        
        self.scheduler = AsyncIOScheduler(timezone=self.scheduler_config.get('timezone', 'Asia/Shanghai'))
        
        # Add jobs for each enabled data type
        self._add_user_jobs()
        self._add_creator_jobs()
        self._add_live_jobs()
        
        self.scheduler.start()
        self.is_running = True
        
        logger.info(f"UnifiedDataScheduler started with data types: {self.enabled_data_types}")

    def _add_user_jobs(self):
        """Add user data collection jobs with advanced scheduling"""
        if 'user' not in self.enabled_data_types:
            return

        schedule_config = get_data_type_schedule('user')

        # Define job configurations
        job_configs = [
            {
                'frequency': 'hourly',
                'task_name': '每小时任务',
                'trigger_type': 'cron',
                'trigger_args': {'minute': 0}
            },
            {
                'frequency': 'daily',
                'task_name': '每日任务',
                'trigger_type': 'cron',
                'trigger_args': {'hour': 0, 'minute': 0}
            },
            {
                'frequency': 'three_day',
                'task_name': '每三日任务',
                'trigger_type': 'cron',
                'trigger_args': {'day': '*/3', 'hour': 0, 'minute': 0}
            },
            {
                'frequency': 'weekly',
                'task_name': '每周任务',
                'trigger_type': 'cron',
                'trigger_args': {'day_of_week': 'mon', 'hour': 3, 'minute': 0}
            },
            {
                'frequency': 'monthly',
                'task_name': '每月任务',
                'trigger_type': 'cron',
                'trigger_args': {'day': 1, 'hour': 0, 'minute': 0}
            }
        ]

        # Add jobs based on configuration
        for job_config in job_configs:
            frequency = job_config['frequency']
            if frequency in schedule_config:
                config = schedule_config[frequency]
                trigger_args = job_config['trigger_args'].copy()
                trigger_type = job_config['trigger_type']

                # Check if config uses interval (should use IntervalTrigger)
                if 'interval' in config:
                    trigger_type = 'interval'
                    trigger_args = {'hours': config['interval']}
                    if frequency == 'hourly':
                        trigger_args = {'hours': config['interval']}
                    elif frequency == 'minute':
                        trigger_args = {'minutes': config['interval']}
                else:
                    # Override with config values for CronTrigger
                    for key, value in config.items():
                        if key != 'jitter' and key != 'mode':
                            trigger_args[key] = value

                # Add jitter if specified
                if 'jitter' in config:
                    trigger_args['jitter'] = config['jitter']

                # Create task function
                async def create_task_func(freq=frequency):
                    return await self.execute_user_functions_safely(freq)

                self._add_task_to_scheduler(
                    create_task_func,
                    job_config['task_name'],
                    trigger_type,
                    **trigger_args
                )

        # Add idle task (till worker) if configured
        if 'till' in schedule_config:
            till_config = schedule_config['till']
            if till_config.get('mode') == 'idle_check':
                interval_minutes = till_config.get('interval', 5)
                jitter = till_config.get('jitter', 30)

                self._add_task_to_scheduler(
                    self.run_idle_task_if_needed,
                    '空闲时间补充任务',
                    'interval',
                    minutes=interval_minutes,
                    jitter=jitter
                )

    def _add_creator_jobs(self):
        """Add creator data collection jobs"""
        if 'creator' not in self.enabled_data_types:
            return
        
        schedule_config = get_data_type_schedule('creator')
        
        if 'daily' in schedule_config:
            daily_config = schedule_config['daily']
            self.scheduler.add_job(
                self.execute_creator_functions_safely,
                'cron',
                hour=daily_config['hour'],
                minute=daily_config['minute'],
                id='creator_daily_task',
                name='Creator Daily Data Collection'
            )

    def _add_live_jobs(self):
        """Add live monitoring jobs"""
        if 'live' not in self.enabled_data_types:
            return

        schedule_config = get_data_type_schedule('live')

        # Add continuous monitoring if configured
        if 'continuous' in schedule_config:
            continuous_config = schedule_config['continuous']
            if continuous_config.get('mode') == 'continuous':
                # For continuous monitoring, we'll start it as a background task
                # rather than a scheduled job
                logger.info("Live continuous monitoring will be started as background task")

        # Add minute-based status updates if configured
        if 'minute' in schedule_config:
            minute_config = schedule_config['minute']
            if 'interval' in minute_config:
                interval_minutes = minute_config['interval']
                jitter = minute_config.get('jitter', 30)

                async def live_status_update():
                    return await self.execute_live_monitoring("once")

                self._add_task_to_scheduler(
                    live_status_update,
                    'Live状态更新任务',
                    'interval',
                    minutes=interval_minutes,
                    jitter=jitter
                )

    def stop_scheduler(self):
        """Stop the unified scheduler"""
        if self.scheduler and self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("UnifiedDataScheduler stopped")
        else:
            logger.warning("Unified scheduler is not running")

        # Stop cookie auto-refresh
        if self.cookie_manager:
            self.cookie_manager.stop_auto_refresh()

    def get_scheduler_status(self):
        """Get scheduler status"""
        if not self.scheduler:
            return {"status": "not_initialized", "jobs": [], "data_types": self.enabled_data_types}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": str(job.next_run_time) if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running" if self.is_running else "stopped",
            "jobs": jobs,
            "data_types": self.enabled_data_types,
            "config": self.scheduler_config
        }
