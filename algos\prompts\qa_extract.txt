You are an expert extraction algorithm. Only extract relevant information from the text. If you do not know the value of an attribute asked to extract, return null for the attribute's value. The following is a Chinese vtuber live streaming record contains the reply of vtuber to the audience's danmu.

### Requirements
1. In order to make the danmu and answers relevant, the danmu can be generated and paraphrased on their own, but the answers should be kept in the same text as possible.
2. The danmu needs to be in line with the audience's tone, with a tendency towards humor.
3. every pair should be individual.

### Examples
Input:
小星星说："瞳瞳今天轮到我过生日了，群星女神能助我找一个合适的工作吗？"啊，祝小星星找到一个合适的工作啊。但是小星星，如果你暂时没有找到特别适合自己的工作呢，也千万千万不要担心。因为就是...嗯，我看到有很多小星星说最近的工作可能没有那么好找啊，而且临近过年了，说什么咱都不如过个好年对吧。
Output:
danmu: "瞳瞳今天轮到我过生日了，群星女神能助我找一个合适的工作吗？"
answer: "啊，祝小星星找到一个合适的工作啊。但是小星星，如果你暂时没有找到特别适合自己的工作呢，也千万千万不要担心。因为就是...嗯，我看到有很多小星星说最近的工作可能没有那么好找啊，而且临近过年了，说什么咱都不如过个好年对吧。"

Input:
哎呀，一上来大家就一直在这里说这个房间里站不下我。小星星，你们有没有听过这样的一个笑话：就是富翁有三个女儿，他说如果你们三个谁找到什么东西把这个房间填满，那我就让谁继承我高昂的财产。第一个女儿找来了一房间的棉花没有填满，第二个女儿找到了一房间的砖头没有填满，第三个女儿找来了星瞳，于是房间里站满了人。你们听过吗，小星星？为什么我今天一上来就在讲冷笑话？啊啊啊啊，为什么我今天一上来就在讲冷笑话？
Output:
danmu: "这个房间里根本站不下主播"
answer: "哎呀，一上来大家就一直在这里说这个房间里站不下我。小星星，你们有没有听过这样的一个笑话：就是富翁有三个女儿，他说如果你们三个谁找到什么东西把这个房间填满，那我就让谁继承我高昂的财产。第一个女儿找来了一房间的棉花没有填满，第二个女儿找到了一房间的砖头没有填满，第三个女儿找来了星瞳，于是房间里站满了人。你们听过吗，小星星？为什么我今天一上来就在讲冷笑话？啊啊啊啊，为什么我今天一上来就在讲冷笑话？"

Input:
之前抽星瞳卡册的时候，抽出了两超出了大隐藏...谁问你了，谁问你了。今天在这个直播间，就算不是我打你，也有人会打你的对吧。我们一会儿公屏上就有小星星要急了啊，直接就急了。主播你自己有大隐藏吗？我有...我有个陪...我有个朋。
Output:
danmu: "之前抽星瞳卡册的时候，抽出了两抽出了大隐藏"
answer: "谁问你了，谁问你了。今天在这个直播间，就算不是我打你，也有人会打你的对吧。我们一会儿公屏上就有小星星要急了啊，直接就急了。"