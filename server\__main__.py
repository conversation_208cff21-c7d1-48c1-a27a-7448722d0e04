"""
Server module main entry point
服务器模块主入口点
"""

import sys
import asyncio

if len(sys.argv) > 1 and sys.argv[1] == 'cookie':
    # Cookie management CLI
    try:
        from .base.cookie_cli import main
        asyncio.run(main())
    except ImportError:
        print("Cookie management CLI not available")
        sys.exit(1)
else:
    print("Available commands:")
    print("  python -m server cookie [args]  - Cookie management CLI")
