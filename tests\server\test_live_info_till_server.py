import unittest
from unittest.mock import MagicMock, patch
import backend.query.query_live_info
from server.live_info_till_server import <PERSON><PERSON><PERSON><PERSON>, insert_live_session_table_sql


class TestSaveLiveSessionToDB(unittest.TestCase):
    def setUp(self):
        # Mock the MyHandler class and its attributes
        self.handler = MyHandler()
        self.handler.room_live_status = {
            "12345": {"live_id": "test_live_id"}
        }
        self.handler.logger = MagicMock()

    @patch("server.live_info_till_server.cursor")
    @patch("server.live_info_till_server.connection")
    @patch("server.live_info_till_server.logger")
    def test_save_live_session_to_db(self, mock_logger, mock_connection, mock_cursor):
        # Mock input parameters
        room_id = "12345"
        live_id = "test_live_id"
        title = "Test Title"
        cover = "Test Cover"
        parent_area = "Parent Area"
        area = "Area"
        start_time_str = "2023-01-01 12:00:00"
        end_time_str = "2023-01-01 14:00:00"
        pay_count = 10
        income = 1000
        watch_change_count = 500
        like_info_update_count = 200
        danmu_count = 300
        interaction_count = 50
        max_online_rank = 100
        seconds = 1672560000
        dt = "2023-01-01 14:00:00"
        
        # Call the function being tested
        self.handler.save_live_session_to_db(
            room_id, live_id, title, cover, parent_area, area, start_time_str, end_time_str,
            pay_count, income, watch_change_count, like_info_update_count,
            danmu_count, interaction_count, max_online_rank, seconds, dt
        )
        
        # Assert that cursor.execute was called with the correct parameters
        expected_params = {
            "room_id": room_id,
            "live_id": "test_live_id",
            "title": title,
            "cover": cover,
            "parent_area": parent_area,
            "area": area,
            "start_time_str": start_time_str,
            "end_time_str": end_time_str,
            "pay_count": pay_count,
            "income": income,
            "watch_change_count": watch_change_count,
            "like_info_update_count": like_info_update_count,
            "danmu_count": danmu_count,
            "interaction_count": interaction_count,
            "max_online_rank": max_online_rank,
            "timestamp": seconds,
            "datetime": dt,
        }
        mock_cursor.execute.assert_called_once_with(insert_live_session_table_sql, expected_params)
        
        # Assert that connection.commit was called
        mock_connection.commit.assert_called_once()
        
        # Assert that logger.info was called with the expected message
        mock_logger.info.assert_called_once_with(
            f"[{room_id}] [{dt}] 存入DB，当前场次 开始时间：{start_time_str}，结束时间：{end_time_str}，"
            f"付费次数：{pay_count}，总营收：{income}，观看人次：{watch_change_count}，点赞次数：{like_info_update_count}"
        )

    @patch("server.live_info_till_server.cursor")
    @patch("server.live_info_till_server.connection")
    @patch("server.live_info_till_server.logger")
    def test_save_live_session_to_db_with_none_live_id(self, mock_logger, mock_connection, mock_cursor):
        # Test case where live_id is None
        self.handler.room_live_status = {
            "67890": {"live_id": "test_live_id"}
        }
        
        room_id = "67890"
        live_id = None
        title = "Another Test"
        cover = "Another Cover"
        parent_area = "Another Parent Area"
        area = "Another Area"
        start_time_str = "2023-02-01 12:00:00"
        end_time_str = "2023-02-01 14:00:00"
        pay_count = 20
        income = 2000
        watch_change_count = 1000
        like_info_update_count = 400
        danmu_count = 600
        interaction_count = 100
        max_online_rank = 200
        seconds = 1675180800
        dt = "2023-02-01 14:00:00"
        
        # Call the function being tested
        self.handler.save_live_session_to_db(
            room_id, live_id, title, cover, parent_area, area, start_time_str, end_time_str,
            pay_count, income, watch_change_count, like_info_update_count,
            danmu_count, interaction_count, max_online_rank, seconds, dt
        )
        
        # Assert that cursor.execute was called with the correct parameters
        expected_params = {
            "room_id": room_id,
            "live_id": None,
            "title": title,
            "cover": cover,
            "parent_area": parent_area,
            "area": area,
            "start_time_str": start_time_str,
            "end_time_str": end_time_str,
            "pay_count": pay_count,
            "income": income,
            "watch_change_count": watch_change_count,
            "like_info_update_count": like_info_update_count,
            "danmu_count": danmu_count,
            "interaction_count": interaction_count,
            "max_online_rank": max_online_rank,
            "timestamp": seconds,
            "datetime": dt,
        }
        mock_cursor.execute.assert_called_once_with(insert_live_session_table_sql, expected_params)
        
        # Assert that connection.commit was called
        mock_connection.commit.assert_called_once()

    @patch("server.live_info_till_server.connection")
    def test_query_whole_live_info_with_live_id(self, mock_connection):
        # Test case where live_id is None
        self.handler.room_live_status = {
            "67890": {"live_id": "test_live_id"}
        }

        room_id = "67890"
        live_id = "test_live_id"
        title = "Another Test"
        cover = "Another Cover"
        parent_area = "Another Parent Area"
        area = "Another Area"
        start_time_str = "2023-02-01 12:00:00"
        end_time_str = "2023-02-01 14:00:00"
        pay_count = 20
        income = 2000
        watch_change_count = 1000
        like_info_update_count = 400
        danmu_count = 600
        interaction_count = 100
        max_online_rank = 200
        seconds = 1675180800
        dt = "2023-02-01 14:00:00"

        # Call the function being tested
        self.handler.save_live_session_to_db(
            room_id, live_id, title, cover, parent_area, area, start_time_str, end_time_str,
            pay_count, income, watch_change_count, like_info_update_count,
            danmu_count, interaction_count, max_online_rank, seconds, dt
        )

        # Mock the return value of query_whole_live_info_with_live_id
        with patch("backend.query.select_live_info.query_whole_live_info_with_live_id") as mock_query:
            mock_query.return_value = {
                "livingInfo": {
                    "liveId": "test_live_id",
                    "startDate": start_time_str,
                    "stopDate": end_time_str,
                    "pay_count": pay_count,
                    "income": income,
                    "watch_change_count": watch_change_count,
                    "like_info_update_count": like_info_update_count,
                    "danmu_count": danmu_count,
                    "interaction_count": interaction_count,
                    "max_online_rank": max_online_rank,
                    "timestamp": seconds,
                    "datetime": dt,
                }
            }
            result = backend.tools.query_live_info.query_whole_live_info_with_live_id("test_live_id")

        self.assertEqual(result["liveId"], "test_live_id")
        self.assertEqual(result["startDate"], start_time_str)
        self.assertEqual(result["stopDate"], end_time_str)
        self.assertEqual(result["pay_count"], pay_count)
        self.assertEqual(result["income"], income)
        self.assertEqual(result["watch_change_count"], watch_change_count)
        self.assertEqual(result["like_info_update_count"], like_info_update_count)
        self.assertEqual(result["danmu_count"], danmu_count)
        self.assertEqual(result["interaction_count"], interaction_count)
        self.assertEqual(result["max_online_rank"], max_online_rank)
        self.assertEqual(result["timestamp"], seconds)
        self.assertEqual(result["datetime"], dt)
        
        # Assert that connection.commit was called
        mock_connection.commit.assert_called_once()

    @patch("server.live_info_till_server.cursor")
    @patch("server.live_info_till_server.connection")
    @patch("server.live_info_till_server.logger")
    @patch("server.live_info_till_server.query_live_start_time_by_end_time")
    @patch("server.live_info_till_server.query_pay_count_by_room_and_live_start_end_time")
    @patch("server.live_info_till_server.query_danmu_by_room_and_datetime")
    @patch("server.live_info_till_server.query_interact_word_count_by_room_and_and_datetime")
    @patch("server.live_info_till_server.query_max_online_rank_count_by_room_and_datetime")
    def test_live_end_trigger_save_session(self, mock_max_online_rank, mock_interact_word_count, 
                                          mock_danmu_count, mock_pay_count, mock_start_time, 
                                          mock_logger, mock_connection, mock_cursor):
        """
        测试当结束直播时间触发时，能否正确执行 insert_live_session_table_sql
        """
        # 设置模拟的直播状态和相关数据
        room_id = "12345"
        live_id = "test_live_id"
        title = "Test Live Stream"
        cover = "http://example.com/cover.jpg"
        parent_area = "娱乐"
        area = "聊天"
        start_time_str = "2023-01-01 12:00:00"
        end_time_str = "2023-01-01 14:00:00"
        seconds = 1672560000
        dt = end_time_str
        
        # 模拟查询结果
        mock_start_time.return_value = (start_time_str, 0.1)
        mock_pay_count.return_value = (10, 1000, [], 0.1)
        mock_danmu_count.return_value = ([], 300)
        mock_interact_word_count.return_value = 50
        mock_max_online_rank.return_value = 100
        
        # 设置handler的初始状态
        self.handler = MyHandler()
        self.handler.room_live_status = {
            room_id: {
                "live_status": 1,  # 1表示直播中
                "live_id": live_id,
                "start_time": start_time_str
            }
        }
        self.handler.temp_live_status_minute_dict = {room_id: 1}  # 当前状态为直播中
        self.handler.is_in_live_symbol_dict = {str(room_id): True}
        self.handler.watch_change_dict = {room_id: 500}
        self.handler.like_info_update_dict = {room_id: 200}
        
        # 模拟API返回的数据
        api_data = {
            "live_status": 0,  # 0表示未开播，模拟直播结束
            "title": title,
            "user_cover": cover,
            "parent_area_name": parent_area,
            "area_name": area,
            "live_time": start_time_str
        }
        
        # 模拟get_live_status方法中的直播结束逻辑
        # 当直播状态从1变为0时，会触发结束直播操作
        if self.handler.temp_live_status_minute_dict[room_id] == 1 and api_data["live_status"] in [0, 2]:
            # 下播时将看过和点赞存入数据库
            self.handler.save_watch_change_to_db = MagicMock()
            self.handler.save_like_info_update_to_db = MagicMock()
            
            # 保存直播会话数据
            self.handler.save_live_session_to_db(
                room_id, live_id, title, cover, parent_area, area, 
                start_time_str, end_time_str, 10, 1000, 500, 200, 
                300, 50, 100, seconds, dt
            )
            # 重置live_id
            self.handler.room_live_status[room_id]['live_id'] = None
        
        # 验证save_live_session_to_db方法是否被正确调用
        expected_params = {
            "room_id": room_id,
            "live_id": live_id,
            "title": title,
            "cover": cover,
            "parent_area": parent_area,
            "area": area,
            "start_time_str": start_time_str,
            "end_time_str": end_time_str,
            "pay_count": 10,
            "income": 1000,
            "watch_change_count": 500,
            "like_info_update_count": 200,
            "danmu_count": 300,
            "interaction_count": 50,
            "max_online_rank": 100,
            "timestamp": seconds,
            "datetime": dt,
        }
        
        # 验证cursor.execute被调用，并且参数正确
        mock_cursor.execute.assert_called_with(insert_live_session_table_sql, expected_params)
        
        # 验证connection.commit被调用
        mock_connection.commit.assert_called_once()
        
        # 验证live_id被重置为None
        self.assertIsNone(self.handler.room_live_status[room_id]['live_id'])
