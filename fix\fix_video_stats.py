#!/usr/bin/env python3
"""
视频统计数据修复脚本
Fix Video Statistics Script

用于修复 videos_table 表中 like_num、coin、favorite_num 等字段的 null 值问题
Fixes null values in like_num, coin, favorite_num fields in videos_table

使用方法 Usage:
    python fix_video_stats.py [options]

选项 Options:
    --uid UID           指定用户ID，只处理该用户的视频
    --batch-size N      批处理大小，默认10
    --delay N           API调用延时（秒），默认5
    --dry-run           试运行模式，只查询不更新
    --retry-failed      重试之前失败的记录
    --help              显示帮助信息
"""

import asyncio
import argparse
import json
import logging
import random
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from bilibili_api import Credential
from server.tools.fetch_video_info_tools import get_all_video_info
from sql.db_pool import get_connection, utils as U

# Import cookie manager for getting cookie values
sys.path.append(str(Path(__file__).parent.parent / "server"))
from cookie_manager import get_sessdata, get_bili_jct, get_buvid3, get_dedeuserid, get_buvid4
from logger import logger

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'fix/fix_video_stats_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class VideoStatsFixer:
    def __init__(self, batch_size: int = 10, delay: float = 5.0, dry_run: bool = False):
        self.batch_size = batch_size
        self.delay = delay
        self.dry_run = dry_run
        self.credential = Credential(
            sessdata=get_sessdata("user"),
            bili_jct=get_bili_jct("user"),
            buvid3=get_buvid3("user"),
            dedeuserid=get_dedeuserid("user"),
            buvid4=get_buvid4("user"),
        )
        self.failed_videos = []
        self.success_count = 0
        self.failed_count = 0
        
    async def get_videos_to_fix(self, uid: Optional[str] = None) -> List[Dict]:
        """获取需要修复的视频列表"""
        query = """
        SELECT bvid, uid, video_name, like_num, coin, favorite_num, share_num, danmuku_num,
               honor_short, honor_count, honor, heat, play_num, comment_num
        FROM videos_table
        WHERE like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
           OR share_num IS NULL OR danmuku_num IS NULL
           OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
           OR heat IS NULL
        """
        params = []

        if uid:
            query += " AND uid = $1"
            params.append(uid)

        query += " ORDER BY uid, bvid"

        async with get_connection() as conn:
            results = await conn.fetch(query, *params)
            return [dict(row) for row in results]
    
    async def get_failed_videos(self) -> List[str]:
        """从日志文件中获取之前失败的视频BVID"""
        failed_file = Path('fix/failed_videos.json')
        if failed_file.exists():
            with open(failed_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    
    async def save_failed_videos(self):
        """保存失败的视频BVID到文件"""
        failed_file = Path('fix/failed_videos.json')
        with open(failed_file, 'w', encoding='utf-8') as f:
            json.dump(self.failed_videos, f, ensure_ascii=False, indent=2)
    
    def get_nested_value(self, d, keys, default=None):
        """安全获取嵌套字典值"""
        for key in keys:
            if isinstance(d, dict):
                d = d.get(key)
            else:
                return default
            if d is None:
                return default
        return d
    
    async def fetch_video_stats(self, bvid: str, video_data: Dict = None) -> Optional[Dict]:
        """获取单个视频的统计信息"""
        try:
            logger.info(f"正在获取视频 {bvid} 的统计信息...")

            # 添加随机延时，模拟人工操作
            base_delay = self.delay
            random_delay = random.uniform(0.5, 1.5)
            total_delay = base_delay + random_delay

            await asyncio.sleep(total_delay)

            # 调用API获取视频信息
            all_res = await get_all_video_info(bvid)

            if not isinstance(all_res, dict) or 'stat' not in all_res:
                logger.warning(f"视频 {bvid} 返回的数据格式不正确: {all_res}")
                return None

            stat = all_res.get('stat', {})

            # 提取基础统计数据
            like_num = U.safe_int(self.get_nested_value(stat, ['like'], 0))
            coin = U.safe_int(self.get_nested_value(stat, ['coin'], 0))
            favorite_num = U.safe_int(self.get_nested_value(stat, ['favorite'], 0))
            share_num = U.safe_int(self.get_nested_value(stat, ['share'], 0))
            danmuku_num = U.safe_int(self.get_nested_value(stat, ['danmaku'], 0))

            # 提取荣誉信息
            honor_short = all_res.get('honor_short', '') or ''
            honor_count = U.safe_int(all_res.get('honor_count', 0))
            honor = all_res.get('honor', '') or ''

            # 获取播放数和评论数用于计算热度
            play_num = U.safe_int(self.get_nested_value(stat, ['view'], 0))
            comment_num = U.safe_int(self.get_nested_value(stat, ['reply'], 0))

            # 如果从数据库传入了现有数据，优先使用数据库中的播放数和评论数
            if video_data:
                play_num = video_data.get('play_num') or play_num
                comment_num = video_data.get('comment_num') or comment_num

            # 计算热度
            heat = round(U.video_calculate_hotness(
                play_num, comment_num, honor_count, like_num, coin, favorite_num
            ), 2)

            stats = {
                'like_num': like_num,
                'coin': coin,
                'favorite_num': favorite_num,
                'share_num': share_num,
                'danmuku_num': danmuku_num,
                'honor_short': honor_short,
                'honor_count': honor_count,
                'honor': honor,
                'heat': heat,
            }

            logger.info(f"视频 {bvid} 统计信息: {stats}")
            return stats

        except Exception as e:
            logger.error(f"获取视频 {bvid} 统计信息失败: {e}")
            return None
    
    async def update_video_stats(self, bvid: str, stats: Dict) -> bool:
        """更新视频统计信息到数据库"""
        if self.dry_run:
            logger.info(f"[DRY RUN] 将更新视频 {bvid} 的统计信息: {stats}")
            return True

        try:
            update_sql = """
            UPDATE videos_table
            SET like_num = $1, coin = $2, favorite_num = $3, share_num = $4, danmuku_num = $5,
                honor_short = $6, honor_count = $7, honor = $8, heat = $9
            WHERE bvid = $10
            """

            async with get_connection() as conn:
                await conn.execute(
                    update_sql,
                    stats['like_num'],
                    stats['coin'],
                    stats['favorite_num'],
                    stats['share_num'],
                    stats['danmuku_num'],
                    stats['honor_short'],
                    stats['honor_count'],
                    stats['honor'],
                    stats['heat'],
                    bvid
                )

            logger.info(f"成功更新视频 {bvid} 的统计信息")
            return True

        except Exception as e:
            logger.error(f"更新视频 {bvid} 统计信息到数据库失败: {e}")
            return False
    
    async def process_videos(self, videos: List[Dict]) -> Tuple[int, int]:
        """批量处理视频"""
        total_videos = len(videos)
        logger.info(f"开始处理 {total_videos} 个视频...")
        
        for i, video in enumerate(videos, 1):
            bvid = video['bvid']
            video_name = video.get('video_name', 'Unknown')
            
            logger.info(f"处理进度: {i}/{total_videos} - 视频: {bvid} ({video_name})")

            # 获取统计信息
            stats = await self.fetch_video_stats(bvid, video)

            if stats is None:
                logger.warning(f"跳过视频 {bvid}，无法获取统计信息")
                self.failed_videos.append(bvid)
                self.failed_count += 1
                continue
            
            # 更新数据库
            if await self.update_video_stats(bvid, stats):
                self.success_count += 1
            else:
                self.failed_videos.append(bvid)
                self.failed_count += 1
            
            # 批量处理间隔
            if i % self.batch_size == 0 and i < total_videos:
                batch_delay = random.uniform(10, 20)  # 批量间隔10-20秒
                logger.info(f"批量处理完成，等待 {batch_delay:.1f} 秒...")
                await asyncio.sleep(batch_delay)
        
        return self.success_count, self.failed_count

    async def run(self, uid: Optional[str] = None, retry_failed: bool = False):
        """运行修复脚本"""
        start_time = time.time()

        try:
            if retry_failed:
                # 重试之前失败的视频
                failed_bvids = await self.get_failed_videos()
                if not failed_bvids:
                    logger.info("没有找到之前失败的视频记录")
                    return

                logger.info(f"重试 {len(failed_bvids)} 个之前失败的视频...")
                videos = []
                for bvid in failed_bvids:
                    videos.append({'bvid': bvid, 'video_name': f'Retry-{bvid}'})
            else:
                # 获取需要修复的视频
                videos = await self.get_videos_to_fix(uid)

                if not videos:
                    logger.info("没有找到需要修复的视频")
                    return

                logger.info(f"找到 {len(videos)} 个需要修复统计信息的视频")

                if not self.dry_run:
                    # 确认操作
                    print(f"\n即将修复 {len(videos)} 个视频的统计信息")
                    if uid:
                        print(f"限制用户ID: {uid}")
                    print(f"批处理大小: {self.batch_size}")
                    print(f"API调用延时: {self.delay} 秒")

                    confirm = input("\n确认继续？(y/N): ").strip().lower()
                    if confirm != 'y':
                        logger.info("用户取消操作")
                        return

            # 处理视频
            success_count, failed_count = await self.process_videos(videos)

            # 保存失败记录
            if self.failed_videos:
                await self.save_failed_videos()

            # 统计结果
            end_time = time.time()
            duration = end_time - start_time

            logger.info(f"\n修复完成!")
            logger.info(f"总处理时间: {duration:.1f} 秒")
            logger.info(f"成功修复: {success_count} 个视频")
            logger.info(f"失败: {failed_count} 个视频")

            if self.failed_videos:
                logger.info(f"失败的视频已保存到: fix/failed_videos.json")

        except KeyboardInterrupt:
            logger.info("\n用户中断操作")
            if self.failed_videos:
                await self.save_failed_videos()
        except Exception as e:
            logger.error(f"修复过程中发生错误: {e}")
            if self.failed_videos:
                await self.save_failed_videos()
            raise


async def main():
    parser = argparse.ArgumentParser(
        description="修复视频统计数据中的null值",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        '--uid',
        type=str,
        help='指定用户ID，只处理该用户的视频'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=10,
        help='批处理大小，默认10'
    )

    parser.add_argument(
        '--delay',
        type=float,
        default=5.0,
        help='API调用延时（秒），默认5.0'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式，只查询不更新'
    )

    parser.add_argument(
        '--retry-failed',
        action='store_true',
        help='重试之前失败的记录'
    )

    args = parser.parse_args()

    # 创建修复器实例
    fixer = VideoStatsFixer(
        batch_size=args.batch_size,
        delay=args.delay,
        dry_run=args.dry_run
    )

    # 运行修复
    await fixer.run(uid=args.uid, retry_failed=args.retry_failed)


if __name__ == "__main__":
    asyncio.run(main())
