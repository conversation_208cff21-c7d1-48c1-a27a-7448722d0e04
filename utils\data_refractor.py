import json
from concurrent.futures import ThreadPoolExecutor


def transfer_haruhi_to_alpaca(input_haruhi_path: str, output_alpaca_path: str):
    """
    Transfer the data from the format of Haruhi to the format of Alpaca.
    Haruui: https://huggingface.co/datasets/silk-road/ChatHaruhi-Expand-118K

    Input: Haruhi54k.jsonl, Chinese15.jsonl, RoleLLM.jsonl -> {"context": str, "target": str"}

    Returns: Haruhi54k_alpaca.jsonl, Chinese15_alpaca.jsonl, RoleLLM_alpaca.jsonl -> {"instruction": str, "input": str, "output": str}
    """
    with open(input_haruhi_path, "r", encoding="utf-8") as fin:
        haruhi_data = []
        for line in fin:
            haruhi_data.append(json.loads(line))

        print(len(haruhi_data))

    with open(output_alpaca_path, "w", encoding="utf-8") as fout:
        res = []
        for data in haruhi_data:
            context = data["context"]
            target = data["target"]

            ## find out text of context behind ""\n\n\n"
            split_context = "Classic scenes for the role are as follows:"
            if split_context in context:
                instruction = context.split(split_context)[0]
                input = split_context + context.split(split_context)[1]
            else:
                instruction = context
                input = ""

            res.append({"instruction": instruction, "input": input, "output": target})
        json.dump(res, fout, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    extract_list = ["Haruhi54K.jsonl", "Chinese15.jsonl", "RoleLLM.jsonl"]
    input_haruhi_abs_path = "/home/<USER>/Halyu/Dev/Code/LLaMA-Factory/datasets/finetune/ChatHaruhi-Expand-118K/"
    output_alpaca_abs_path = (
        "/home/<USER>/Halyu/Dev/Code/LLaMA-Factory/data/chatharuhi/"
    )

    with ThreadPoolExecutor() as executor:
        futures = []
        for i in range(len(extract_list)):
            input_path = input_haruhi_abs_path + extract_list[i]
            output_path = (
                output_alpaca_abs_path + extract_list[i].split(".")[0] + "_alpaca.json"
            )

            future = executor.submit(transfer_haruhi_to_alpaca, input_path, output_path)
            futures.append(future)

        for future in futures:
            future.result()
