"""
Unified Data Scheduler Main Entry Point
"""

import asyncio
import signal
import sys
import time
import multiprocessing
import os
import json
import psutil
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict, Any

try:
    from logger import logger
except ImportError:
    from server.base.config import get_logger
    logger = get_logger()

from server.unified_data_scheduler import UnifiedDataScheduler
from server.base.unified_scheduler_config import get_enabled_data_types, update_data_type_status


class ProcessManager:
    """
    Process Manager for Unified Scheduler
    Handles PID files, process status, and graceful shutdown
    """

    def __init__(self, base_dir: str = "server/runtime"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        self.pid_file = self.base_dir / "unified_scheduler.pid"
        self.status_file = self.base_dir / "unified_scheduler.status"
        self.log_dir = self.base_dir / "logs"
        self.log_dir.mkdir(exist_ok=True)

    def write_pid(self, pid: int = None):
        """Write current process PID to file"""
        if pid is None:
            pid = os.getpid()

        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(pid))
            logger.info(f"PID {pid} written to {self.pid_file}")
        except Exception as e:
            logger.error(f"Failed to write PID file: {e}")

    def read_pid(self) -> Optional[int]:
        """Read PID from file"""
        try:
            if self.pid_file.exists():
                with open(self.pid_file, 'r') as f:
                    return int(f.read().strip())
        except Exception as e:
            logger.error(f"Failed to read PID file: {e}")
        return None

    def remove_pid(self):
        """Remove PID file"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
                logger.info(f"PID file {self.pid_file} removed")
        except Exception as e:
            logger.error(f"Failed to remove PID file: {e}")

    def is_process_running(self, pid: int) -> bool:
        """Check if process with given PID is running"""
        try:
            return psutil.pid_exists(pid)
        except Exception:
            return False

    def get_process_info(self, pid: int) -> Optional[Dict[str, Any]]:
        """Get detailed process information"""
        try:
            if not self.is_process_running(pid):
                return None

            proc = psutil.Process(pid)
            return {
                'pid': pid,
                'name': proc.name(),
                'status': proc.status(),
                'create_time': datetime.fromtimestamp(proc.create_time()),
                'cpu_percent': proc.cpu_percent(),
                'memory_info': proc.memory_info()._asdict(),
                'cmdline': proc.cmdline()
            }
        except Exception as e:
            logger.error(f"Failed to get process info for PID {pid}: {e}")
            return None

    def write_status(self, status: Dict[str, Any]):
        """Write status information to file"""
        try:
            status['timestamp'] = datetime.now().isoformat()
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to write status file: {e}")

    def read_status(self) -> Optional[Dict[str, Any]]:
        """Read status information from file"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to read status file: {e}")
        return None

    def stop_process(self, pid: int, timeout: int = 30) -> bool:
        """Gracefully stop process with given PID"""
        try:
            if not self.is_process_running(pid):
                logger.info(f"Process {pid} is not running")
                return True

            proc = psutil.Process(pid)
            logger.info(f"Sending SIGTERM to process {pid}")
            proc.terminate()

            # Wait for graceful shutdown
            try:
                proc.wait(timeout=timeout)
                logger.info(f"Process {pid} terminated gracefully")
                return True
            except psutil.TimeoutExpired:
                logger.warning(f"Process {pid} did not terminate gracefully, sending SIGKILL")
                proc.kill()
                proc.wait(timeout=5)
                logger.info(f"Process {pid} killed forcefully")
                return True

        except Exception as e:
            logger.error(f"Failed to stop process {pid}: {e}")
            return False

    def find_scheduler_processes(self) -> List[Dict[str, Any]]:
        """Find all running unified scheduler processes"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'unified_scheduler_main.py' in cmdline:
                        processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'create_time': datetime.fromtimestamp(proc.info['create_time']),
                            'process_info': self.get_process_info(proc.info['pid'])
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"Error finding scheduler processes: {e}")

        return processes


class UnifiedSchedulerManager:
    """
    Unified Scheduler Manager
    Manages the lifecycle of the unified data scheduler
    """

    def __init__(self, data_types: List[str] = None, uid: str = "401315430"):
        """
        Initialize scheduler manager

        Args:
            data_types: List of data types to enable ['user', 'creator', 'live', 'all']
            uid: User ID for creator data
        """
        self.data_types = data_types or get_enabled_data_types()
        self.uid = uid
        self.scheduler = None
        self.is_running = False
        self.process_manager = ProcessManager()
        self.start_time = None
        self.last_health_check = None
        self.error_count = 0
        self.max_errors = 10

        logger.info(f"UnifiedSchedulerManager initialized with data types: {self.data_types}")

    async def initialize_and_start(self):
        """Initialize and start the unified scheduler"""
        try:
            self.start_time = datetime.now()

            # Write PID file
            self.process_manager.write_pid()

            logger.info(f"Initializing Unified Data Scheduler for data types: {self.data_types}")

            # Create scheduler instance
            self.scheduler = UnifiedDataScheduler(data_types=self.data_types, uid=self.uid)

            # Initialize
            await self.scheduler.initialize()

            # Start scheduler
            self.scheduler.start_scheduler()

            self.is_running = True
            logger.info("Unified Data Scheduler started successfully")

            # Write initial status
            self._update_status("running", "Scheduler started successfully")

            # Keep running with health checks
            while self.is_running:
                await self._health_check()
                await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            logger.error(f"Error in unified scheduler manager: {e}")
            self._update_status("error", f"Initialization failed: {e}")
            raise
        finally:
            # Cleanup on exit
            self.process_manager.remove_pid()

    async def _health_check(self):
        """Perform health check and update status"""
        try:
            self.last_health_check = datetime.now()

            # Check scheduler status
            scheduler_status = self.get_status()

            # Update status file
            self._update_status("running", "Health check passed", scheduler_status)

            # Reset error count on successful health check
            self.error_count = 0

        except Exception as e:
            self.error_count += 1
            logger.error(f"Health check failed: {e}")
            self._update_status("warning", f"Health check failed: {e}")

            # Stop if too many errors
            if self.error_count >= self.max_errors:
                logger.error(f"Too many errors ({self.error_count}), stopping scheduler")
                self.stop()

    def _update_status(self, status: str, message: str, extra_data: Dict[str, Any] = None):
        """Update status file with current information"""
        status_data = {
            'status': status,
            'message': message,
            'pid': os.getpid(),
            'data_types': self.data_types,
            'uid': self.uid,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'error_count': self.error_count,
            'is_running': self.is_running
        }

        if extra_data:
            status_data.update(extra_data)

        self.process_manager.write_status(status_data)

    def stop(self):
        """Stop the scheduler"""
        logger.info("Stopping Unified Scheduler Manager...")

        try:
            self.is_running = False

            if self.scheduler:
                self.scheduler.stop_scheduler()
                logger.info("Scheduler stopped")

            # Update status
            self._update_status("stopped", "Scheduler stopped gracefully")

            # Remove PID file
            self.process_manager.remove_pid()

            logger.info("Unified Scheduler Manager stopped successfully")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
            self._update_status("error", f"Shutdown error: {e}")

    def get_status(self):
        """Get status"""
        if self.scheduler:
            return self.scheduler.get_scheduler_status()
        return {"status": "not_initialized", "jobs": [], "data_types": self.data_types}


def stop_all_processes():
    """Stop all running unified scheduler processes"""
    process_manager = ProcessManager()

    print("=== Stopping Unified Data Scheduler Processes ===")

    # Find all running processes
    processes = process_manager.find_scheduler_processes()

    if not processes:
        print("No unified scheduler processes found")
        return True

    print(f"Found {len(processes)} running processes:")
    for proc in processes:
        print(f"  PID: {proc['pid']}, Command: {proc['cmdline']}")

    # Stop each process
    success_count = 0
    for proc in processes:
        pid = proc['pid']
        print(f"\nStopping process {pid}...")

        if process_manager.stop_process(pid):
            print(f"Process {pid} stopped successfully")
            success_count += 1
        else:
            print(f"Failed to stop process {pid}")

    # Clean up PID files
    process_manager.remove_pid()

    print(f"\nStopped {success_count}/{len(processes)} processes")
    return success_count == len(processes)


def signal_handler(signum, frame):
    """Signal handler for graceful shutdown"""
    logger.info(f"Received signal {signum}, shutting down...")

    # Try to stop gracefully
    try:
        process_manager = ProcessManager()
        process_manager.remove_pid()
    except Exception as e:
        logger.error(f"Error during signal handler cleanup: {e}")

    sys.exit(0)


async def run_once(data_types: List[str] = None, uid: str = "401315430"):
    """
    Run scheduler once (for testing)
    """
    logger.info("Running unified scheduler once...")
    
    try:
        scheduler = UnifiedDataScheduler(data_types=data_types, uid=uid)
        await scheduler.initialize()
        
        # Execute tasks based on data types
        if not data_types or 'user' in data_types or 'all' in data_types:
            await scheduler.execute_user_functions_safely('daily')
        
        if not data_types or 'creator' in data_types or 'all' in data_types:
            await scheduler.execute_creator_functions_safely()
        
        if not data_types or 'live' in data_types or 'all' in data_types:
            await scheduler.execute_live_monitoring()
        
        logger.info("One-time execution completed successfully")
        
    except Exception as e:
        logger.error(f"Error in one-time execution: {e}")
        raise


def show_status():
    """Show current scheduler status with detailed information"""
    process_manager = ProcessManager()

    print("=== Unified Data Scheduler Status ===")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Show configuration
    print("Configuration:")
    print(f"  Enabled data types: {get_enabled_data_types()}")
    print()

    # Read status from file
    status_data = process_manager.read_status()
    if status_data:
        print("Last Status Record:")
        print(f"  Status: {status_data.get('status', 'unknown')}")
        print(f"  Message: {status_data.get('message', 'N/A')}")
        print(f"  PID: {status_data.get('pid', 'N/A')}")
        print(f"  Data Types: {status_data.get('data_types', [])}")
        print(f"  Start Time: {status_data.get('start_time', 'N/A')}")
        print(f"  Last Health Check: {status_data.get('last_health_check', 'N/A')}")
        print(f"  Error Count: {status_data.get('error_count', 0)}")
        print(f"  Is Running: {status_data.get('is_running', False)}")
        print()

    # Check PID file
    pid = process_manager.read_pid()
    if pid:
        print(f"PID File: {pid}")
        if process_manager.is_process_running(pid):
            proc_info = process_manager.get_process_info(pid)
            if proc_info:
                print(f"  Process Status: {proc_info['status']}")
                print(f"  CPU Usage: {proc_info['cpu_percent']}%")
                print(f"  Memory Usage: {proc_info['memory_info']['rss'] / 1024 / 1024:.1f} MB")
                print(f"  Create Time: {proc_info['create_time']}")
            else:
                print("  Process Status: Not accessible")
        else:
            print("  Process Status: Not running (stale PID file)")
        print()
    else:
        print("PID File: Not found")
        print()

    # Find all running processes
    processes = process_manager.find_scheduler_processes()
    if processes:
        print(f"Running Processes ({len(processes)}):")
        for i, proc in enumerate(processes, 1):
            print(f"  {i}. PID: {proc['pid']}")
            print(f"     Command: {proc['cmdline']}")
            print(f"     Started: {proc['create_time']}")
            if proc['process_info']:
                info = proc['process_info']
                print(f"     Status: {info['status']}")
                print(f"     CPU: {info['cpu_percent']}%")
                print(f"     Memory: {info['memory_info']['rss'] / 1024 / 1024:.1f} MB")
            print()
    else:
        print("Running Processes: None found")
        print()

    # Show log directory info
    if process_manager.log_dir.exists():
        log_files = list(process_manager.log_dir.glob("*.log"))
        if log_files:
            print(f"Log Files ({len(log_files)}):")
            for log_file in sorted(log_files):
                try:
                    stat = log_file.stat()
                    size_mb = stat.st_size / 1024 / 1024
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    print(f"  {log_file.name}: {size_mb:.1f} MB (modified: {mtime})")
                except Exception as e:
                    print(f"  {log_file.name}: Error reading file info - {e}")
        else:
            print("Log Files: None found")
    else:
        print("Log Directory: Not found")

    print("=" * 50)


def worker(data_types: List[str] = None, uid: str = "401315430"):
    """Worker process for the scheduler"""
    while True:
        try:
            async def main_worker():
                manager = UnifiedSchedulerManager(data_types=data_types, uid=uid)
                await manager.initialize_and_start()
            
            asyncio.run(main_worker())
            
        except Exception as e:
            logger.error(f"Worker encountered an error: {e}")
            time.sleep(5)  # Wait before restarting


def monitor(data_types: List[str] = None, uid: str = "401315430"):
    """Monitor and restart worker process if it crashes with enhanced monitoring"""
    process_manager = ProcessManager()
    restart_count = 0
    max_restarts = 10
    restart_window = 3600  # 1 hour
    restart_times = []

    logger.info("Starting unified scheduler in monitor mode...")

    # Write monitor PID
    process_manager.write_pid()

    try:
        while True:
            try:
                current_time = time.time()

                # Clean old restart times (outside window)
                restart_times = [t for t in restart_times if current_time - t < restart_window]

                # Check if too many restarts
                if len(restart_times) >= max_restarts:
                    logger.error(f"Too many restarts ({len(restart_times)}) in the last hour, stopping monitor")
                    process_manager.write_status({
                        'status': 'error',
                        'message': 'Too many restarts, monitor stopped',
                        'restart_count': len(restart_times)
                    })
                    break

                logger.info(f"Starting worker process (restart #{restart_count})")

                # Update status
                status_data = {
                    'monitor_pid': os.getpid(),
                    'restart_count': restart_count,
                    'restart_times': len(restart_times),
                    'data_types': data_types,
                    'uid': uid
                }
                process_manager.write_status({
                    'status': 'monitoring',
                    'message': f'Monitor active, worker restart #{restart_count}',
                    **status_data
                })

                # Start worker process
                p = multiprocessing.Process(target=worker, args=(data_types, uid))
                p.start()

                worker_start_time = time.time()
                logger.info(f"Worker process started with PID {p.pid}")

                # Monitor worker process
                while p.is_alive():
                    p.join(timeout=30)  # Check every 30 seconds

                    # Update monitor status
                    uptime = time.time() - worker_start_time
                    process_manager.write_status({
                        'status': 'monitoring',
                        'message': f'Worker running (uptime: {uptime:.0f}s)',
                        'worker_pid': p.pid,
                        'worker_uptime': uptime,
                        **status_data
                    })

                # Worker process ended
                exit_code = p.exitcode
                uptime = time.time() - worker_start_time

                if exit_code == 0:
                    logger.info(f"Worker process ended normally after {uptime:.0f}s")
                    break  # Normal exit, don't restart
                else:
                    logger.warning(f"Worker process ended with exit code {exit_code} after {uptime:.0f}s")

                    # Record restart time
                    restart_times.append(current_time)
                    restart_count += 1

                    # Wait before restarting (exponential backoff)
                    wait_time = min(60, 5 * (2 ** min(restart_count, 5)))
                    logger.info(f"Waiting {wait_time}s before restart...")
                    time.sleep(wait_time)

            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt, stopping monitor...")
                if 'p' in locals() and p.is_alive():
                    logger.info("Terminating worker process...")
                    p.terminate()
                    p.join(timeout=30)
                    if p.is_alive():
                        logger.warning("Worker process did not terminate, killing...")
                        p.kill()
                        p.join()
                break

            except Exception as e:
                logger.error(f"Monitor encountered an error: {e}", exc_info=True)
                restart_count += 1

                # Update error status
                process_manager.write_status({
                    'status': 'error',
                    'message': f'Monitor error: {e}',
                    'restart_count': restart_count
                })

                time.sleep(10)  # Wait before retry

    finally:
        # Cleanup
        process_manager.write_status({
            'status': 'stopped',
            'message': 'Monitor stopped',
            'final_restart_count': restart_count
        })
        process_manager.remove_pid()
        logger.info("Monitor stopped")


async def main():
    """Main function for direct execution"""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create manager
    manager = UnifiedSchedulerManager()
    
    try:
        await manager.initialize_and_start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
    finally:
        manager.stop()


def print_help():
    """Print help information"""
    help_text = """
Unified Data Scheduler

Usage:
    python unified_scheduler_main.py [options]

Options:
    --mode MODE         运行模式 (monitor|once|status|stop|help) [default: monitor]
    --data-types TYPES  数据类型，逗号分隔 (user,creator,live,all) [default: all enabled types]
    --uid UID          用户UID [default: 401315430]
    --help             显示帮助信息

Modes:
    monitor  - 监控模式，持续运行并自动重启
    once     - 单次执行模式，执行一次后退出
    status   - 显示当前运行状态和详细信息
    stop     - 优雅停止所有正在运行的调度器进程
    help     - 显示此帮助信息

Data Types:
    user     - 用户数据 (粉丝数、动态、视频等)
    creator  - 创作者数据 (现有creator_info_server功能)
    live     - 直播数据 (直播状态、弹幕等)
    all      - 所有数据类型

Examples:
    python unified_scheduler_main.py --mode monitor
    python unified_scheduler_main.py --mode once --data-types user,creator
    python unified_scheduler_main.py --mode status
    python unified_scheduler_main.py --mode stop

Process Management:
    - PID files are stored in server/runtime/
    - Status information is logged to server/runtime/unified_scheduler.status
    - Logs are stored in server/runtime/logs/
    - Use 'status' mode to check current state
    - Use 'stop' mode to gracefully shutdown all processes
    """
    print(help_text)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Unified Data Scheduler')
    parser.add_argument('--mode', choices=['monitor', 'once', 'status', 'stop', 'help'],
                       default='monitor', help='运行模式')
    parser.add_argument('--data-types', type=str, help='数据类型，逗号分隔 (user,creator,live,all)')
    parser.add_argument('--uid', default='401315430', help='用户UID')

    args = parser.parse_args()

    # Parse data types
    data_types = None
    if args.data_types:
        data_types = [dt.strip() for dt in args.data_types.split(',')]

    try:
        if args.mode == 'help':
            print_help()
        elif args.mode == 'monitor':
            logger.info("Starting in monitor mode...")
            monitor(data_types=data_types, uid=args.uid)
        elif args.mode == 'once':
            logger.info("Running once...")
            asyncio.run(run_once(data_types=data_types, uid=args.uid))
        elif args.mode == 'status':
            show_status()
        elif args.mode == 'stop':
            logger.info("Stopping all scheduler processes...")
            success = stop_all_processes()
            if success:
                print("All processes stopped successfully")
                sys.exit(0)
            else:
                print("Some processes failed to stop")
                sys.exit(1)
        else:
            print_help()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        print("\nOperation interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        print(f"Error: {e}")
        sys.exit(1)
