import asyncpg
from datetime import datetime
from sql.db_pool import get_connection
from sql.web_sql import create_web_user_info_table, insert_web_user_info, select_web_user_info, update_web_user_info
from logger import logger
from passlib.context import CryptContext


pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    """Verifies a plain password against a hashed password."""
    if hashed_password is None:
        return False
    return pwd_context.verify(plain_password, hashed_password)

async def check_web_user_info_table():
    """
    Checks if the web_user_info table exists, and creates it if it doesn't.
    检查 web_user_info 表是否存在，如果不存在则创建它。
    """
    try:
        async with get_connection() as conn:
            await conn.execute(create_web_user_info_table)
            logger.info("Table web_user_info checked/created successfully.")
            return True
    except Exception as e:
        logger.error(f"Error checking/creating web_user_info table: {e}")
        return False

# 用户信息 注册：（将用户信息写入）
async def register_web_user_info(username: str, password_hash: str, email: str, phone: str= None):
    """
    Registers a new web user.
    注册新用户。
    Args:
        username (str): The username. 用户名
        password_hash (str): The hashed password. 哈希后的密码
        email (str): The email address. 邮箱地址
        phone (str): The phone number. 电话号码
    Returns:
        bool: True if registration is successful, False otherwise. 如果注册成功返回 True，否则返回 False。
    """
    current_time = datetime.now()
    try:
        async with get_connection() as conn:
            await check_web_user_info_table()
            await conn.execute(insert_web_user_info, username, password_hash, email, phone, current_time, current_time)
            logger.info(f"User {username} registered successfully.")
            return True
    except asyncpg.exceptions.UniqueViolationError:
        logger.warning(f"Failed to register user {username}: Username, email, or phone already exists.")
        return False # 或者可以抛出特定的异常
    except Exception as e:
        logger.error(f"Error registering user {username}: {e}")
        return False

# 用户信息 登录：（查询用户信息是否在数据库中，并校验密码）
async def login_web_user_info(email: str, password: str):
    """
    Retrieves user information for login and verifies password.
    检索用于登录的用户信息，并验证密码。
    Args:
        email (str): The user's email. 用户邮箱
        password (str): The user's plain text password. 用户明文密码
    Returns:
        dict or None: User data if found and password matches, else None. 如果找到用户数据且密码匹配则返回字典，否则返回 None。
    """
    try:
        async with get_connection() as conn:
            row = await conn.fetchrow(select_web_user_info, email)
            if row:
                # Verify password
                if verify_password(password, row['password']):
                    logger.info(f"User with email {email} authenticated successfully.")
                    # 更新最后登录时间
                    await conn.execute("UPDATE web_user_info SET last_enter_time = $1 WHERE email = $2", datetime.now(), email)
                    return dict(row)
                else:
                    logger.warning(f"Authentication failed for user with email {email}: Invalid password.")
                    return None
            else:
                logger.info(f"User with email {email} not found for login attempt.")
                return None
    except Exception as e:
        logger.error(f"Error during login for user with email {email}: {e}")
        return None

async def get_web_user_by_email(email: str):
    """
    Retrieves user information by email without password verification.
    通过邮箱检索用户信息，不进行密码验证。
    Args:
        email (str): The user's email. 用户邮箱
    Returns:
        dict or None: User data if found, else None. 如果找到用户数据则返回字典，否则返回 None。
    """
    try:
        async with get_connection() as conn:
            row = await conn.fetchrow(select_web_user_info, email)
            if row:
                logger.info(f"User data retrieved for email {email}.")
                return dict(row)
            else:
                logger.info(f"User with email {email} not found.")
                return None
    except Exception as e:
        logger.error(f"Error fetching user by email {email}: {e}")
        return None

# 用户信息 修改：（用于忘记密码/更新密码）
async def modify_web_user_password(email: str, new_password_hash: str):
    """
    Modifies a user's password.
    修改用户密码。
    Args:
        email (str): The user's email. 用户邮箱
        new_password_hash (str): The new hashed password. 新的哈希密码
    Returns:
        bool: True if password modification is successful, False otherwise. 如果密码修改成功返回 True，否则返回 False。
    """
    try:
        async with get_connection() as conn:
            result = await conn.execute(update_web_user_info, new_password_hash, email)
            if result == "UPDATE 1": # asyncpg execute returns 'COMMAND_TAG COUNT'
                logger.info(f"Password for user with email {email} updated successfully.")
                return True
            else:
                logger.warning(f"Failed to update password for user with email {email}. User may not exist or no change made.")
                return False # 用户可能不存在或密码未更改
    except Exception as e:
        logger.error(f"Error updating password for user with email {email}: {e}")
        return False

# 用户信息 删除：(注意：此操作不可逆，通常需要更多安全措施)
async def delete_web_user_info(email: str):
    """
    Deletes a web user by email.
    通过邮箱删除用户。
    Args:
        email (str): The user's email. 用户邮箱
    Returns:
        bool: True if deletion is successful, False otherwise. 如果删除成功返回 True，否则返回 False。
    """
    delete_sql = "DELETE FROM web_user_info WHERE email = $1;"
    try:
        async with get_connection() as conn:
            result = await conn.execute(delete_sql, email)
            if result == "DELETE 1":
                logger.info(f"User with email {email} deleted successfully.")
                return True
            else:
                logger.warning(f"Failed to delete user with email {email}. User may not exist.")
                return False # 用户可能不存在
    except Exception as e:
        logger.error(f"Error deleting user with email {email}: {e}")
        return False

# 确保在应用启动时调用一次建表函数
# 可以在 main.py 或 app.py 的启动逻辑中加入
# import asyncio
# asyncio.run(check_web_user_info_table())
